<template>
  <div class="w-[752px] center-wrapper">
    <div class="text-[#1D335C] mb-[12px]">
      <span class="a-icon-zhanghushezhi text-[18px] mr-[8px]"></span>
      <span class="text-[18px] font-bold">账号设置</span>
    </div>
    <div class="mb-[40px] border border-[#E6E9F0] rounded-[8px]">
      <div class="bg-[#E6EDFE] flex items-center p-[25px] rounded-t-[8px]">
        <!-- <a-image class="rounded-[50%]" :src="userInfo.avatar" :width="78" :height="78"></a-image> -->
        <img-upload v-model="userInfo.avatar" round size="78px"></img-upload>
        <div class="ml-[25px]">
          <div class="text-secondary text-[24px] font-bold mb-[10px]">{{ userInfo.realname }}</div>
          <div>
            <span class="text-secondary text-[16px] mr-[4px]">{{ renderDict(userInfo.sex, 'sex') }}</span>
            <span v-if="userInfo.sex === 1" class="a-icon-male-solid text-primary"></span>
            <span v-else class="a-icon-female-solid text-[#FAB700]"></span>
          </div>
        </div>
      </div>
      <div class="m-[24px] text-secondary">
        <span class="w-[80px] mr-[40px] inline-block">登录账号</span>
        <span>{{ userInfo.username }}</span>
      </div>
      <div class="flex justify-between items-center m-[24px]">
        <div class="text-secondary">
          <span class="w-[80px] mr-[40px] inline-block">登录密码</span>
          <span>**************</span>
        </div>
        <div>
          <span class="primary-btn" @click="changePw">修改密码</span>
        </div>
      </div>
      <div class="flex justify-between items-center m-[24px]">
        <div class="text-secondary">
          <span class="w-[80px] mr-[40px] inline-block">绑定手机号</span>
          <span v-if="userInfo.phone">{{ userInfo.phone }}</span>
          <span v-else class="a-icon-tips text-[14px] text-[#FAB700]">未完善</span>
        </div>
        <div>
          <span class="text-tertiary">（用于接收通知及找回密码等）</span>
          <template v-if="userInfo.phone">
            <span class="primary-btn" @click="unbind(1)">解绑</span>
            <span class="primary-btn" @click="changePhone(1)">修改手机号</span>
          </template>
          <span v-else class="primary-btn" @click="changePhone">立即绑定</span>
        </div>
      </div>
      <div class="flex justify-between items-center m-[24px]">
        <div class="text-secondary">
          <span class="w-[80px] mr-[40px] inline-block">绑定邮箱</span>
          <span>{{ userInfo.email }}</span>
        </div>
        <div>
          <span class="text-tertiary">（用于接收通知及找回密码等）</span>
          <template v-if="userInfo.email">
            <span class="primary-btn" @click="unbind">解绑</span>
            <span class="primary-btn" @click="changeEmail(1)">修改邮箱</span>
          </template>
          <span v-else class="primary-btn" @click="changeEmail">立即绑定</span>
        </div>
      </div>
    </div>
    <div class="text-[#1D335C] mb-[12px]">
      <span class="a-icon-zuzhigoujia text-[18px] mr-[8px]"></span>
      <span class="text-[18px] font-bold">组织架构信息</span>
    </div>

    <div class="mb-[40px] border border-[#E6E9F0] rounded-[8px] p-[24px]">
      <div class="text-[#1D335C] mb-[16px]">所在部门</div>
      <div class="grid grid-cols-3 grid-rows-1 gap-4 text-[14px] text-[#8992A3] pb-[8px] pl-[24px] pr-[24px]">
        <span>部门名称</span>
        <span class="text-center">身份</span>
        <span class="text-right">业务员</span>
      </div>

      <div
        class="dept-item grid grid-cols-3 grid-rows-1 gap-4 text-[16px] text-[#8992A3] p-[24px]"
        :class="item.salesperson ? 'bg-[#EDFBE2] rounded-[8px]' : ''"
        v-for="item in departmentList"
        :key="item.id"
      >
        <span>{{ item.name }}</span>
        <span class="text-center">{{ item.role }}</span>
        <span class="text-right">
          <span
            v-if="item.salesperson"
            class="a-icon-selected-circle mr-[8px]"
            :class="item.salesperson ? 'text-[#6EC21B]' : 'text-primary'"
          ></span>
          <span :class="item.salesperson ? 'text-[#6EC21B]' : 'text-primary'">{{ item.salesperson_text }}</span>
        </span>
      </div>
    </div>
    <!-- 修改密码 -->
    <change-password ref="changePasswordRef"></change-password>
    <!-- 修改手机号 -->
    <bind-phone ref="changePhoneRef"></bind-phone>
    <!-- 修改邮箱 -->
    <bind-email ref="changeEmailRef"></bind-email>
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { unbindPhone, unbindEmail } from '@/apis/common'
import ChangePassword from '@/components/ChangePassword.vue'
import BindPhone from '@/components/ChangePhone.vue'
import BindEmail from '@/components/ChangeEmail.vue'
import { useUserStore } from '@/store/modules/user'
import { renderDict } from '@/utils/render'
const store = useUserStore()
const userInfo = ref({})
userInfo.value = JSON.parse(JSON.stringify(store.userInfo))
const departmentList = ref([
  { id: '1', name: '厦门轻工集团园区运营有限公司', role: '业务人员', salesperson: true, salesperson_text: '当前身份' },
  { id: '2', name: '厦门轻工集团有限公司', role: '业务人员', salesperson: false, salesperson_text: '切换到该身份' },
  {
    id: '3',
    name: '厦门融达信信息技术有限公司',
    role: '业务人员',
    salesperson: false,
    salesperson_text: '切换到该身份'
  }
])
// 修改密码
const changePasswordRef = ref()
const changePw = () => {
  changePasswordRef?.value.open()
}
// 修改手机号
const changePhoneRef = ref()
const changePhone = (type) => {
  const params = type ? { phone: userInfo.value.phone } : {}
  changePhoneRef?.value.open(params)
}
// 解绑
const unbind = (type) => {
  const requestFunc = type ? unbindPhone : unbindEmail
  Modal.confirm({
    title: '提示',
    content: type ? '确定解绑手机号？' : '确定解绑邮箱？',
    centered: true,
    onOk: async () => {
      const { result } = await requestFunc()
      message.success(result)
    }
  })
}
// 修改邮箱
const changeEmailRef = ref()
const changeEmail = (type) => {
  const params = type ? { email: userInfo.value.email } : {}
  changeEmailRef?.value.open(params)
}
</script>

<style lang="less" scoped>
.center-wrapper {
  margin: 40px auto;
  padding: 0;
}
.dept-item {
  &:not(:last-child) {
    border-bottom: 1px solid #e6e9f0;
  }
  &:first-of-type {
    border-top: 1px solid #e6e9f0;
  }
}
</style>
