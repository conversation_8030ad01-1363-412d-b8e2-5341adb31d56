<template>
  <div>
    <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">租赁单元基础信息</h4>
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="单元名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入单元名称" show-count :maxlength="50" />
      </a-form-item>
      <a-form-item label="资产产权" name="virtualLeaseUnit">
        <div class="flex">
          <a-radio-group v-model:value="formData.virtualLeaseUnit">
            <a-radio :value="false">无产权</a-radio>
            <a-radio :value="true">有产权</a-radio>
          </a-radio-group>
          <assets-select
            v-if="formData.virtualLeaseUnit === true"
            v-model:model-value="formData.houseOwner"
            placeholder="选择资产"
            @update:model-value="handleAssetChange"
          />
        </div>
      </a-form-item>
      <a-form-item label="产权用途" name="propertyUse">
        <dict-select v-model="formData.propertyUse" placeholder="产权用途" code="CT_BAS_PropertyUse"></dict-select>
      </a-form-item>
      <a-form-item label="所属项目" name="wyProject">
        <a-cascader
          v-model:value="formData.wyProjectArray"
          :options="projectOptions"
          :field-names="{ label: 'label', value: 'value', children: 'children' }"
          :load-data="loadBuildingFloorData"
          placeholder="请选择项目/楼栋/楼层"
          @change="handleProjectChange"
        />
      </a-form-item>
      <a-form-item label="地址" name="pcaCodeArray">
        <div class="flex">
          <a-cascader
            class="!mr-[4px]"
            v-model:value="formData.pcaCodeArray"
            :options="areaList"
            placeholder="请选择区域"
          />
          <a-input v-model:value="formData.detailAddress" placeholder="请输入详细地址" allow-clear :maxlength="64" />
        </div>
      </a-form-item>
      <a-form-item label="资产类型" name="assetType">
        <dict-select
          v-model="formData.assetType"
          placeholder="资产类型"
          code="CT_BASE_ENUM_LeaseUnit_AssetType"
        ></dict-select>
      </a-form-item>
      <a-form-item label="资产权属公司" name="ownerCompany">
        <dept-tree-select
          v-model="formData.ownerCompany"
          placeholder="请选择资产权属公司"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="租金归集公司" name="collectionCompany">
        <dept-tree-select
          v-model="formData.collectionCompany"
          placeholder="请选择租金归集公司"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="物业管理公司" name="manageCompany">
        <dept-tree-select
          v-model="formData.manageCompany"
          placeholder="请选择物业管理公司"
          type="company"
        ></dept-tree-select>
      </a-form-item>
      <a-form-item label="使用权类型" name="landNature">
        <dict-select v-model="formData.landNature" placeholder="使用权类型" code="CT_BAS_LandNature"></dict-select>
      </a-form-item>
      <a-form-item label="租赁单元类别" name="treeId">
        <api-tree-select
          v-model="formData.treeId"
          :async-fn="getLeaseUnitTree"
          placeholder="请选择租赁单元类别"
        ></api-tree-select>
      </a-form-item>
      <a-form-item label="配套设施" name="supportFacility" class="form-item-full">
        <a-textarea
          v-model:value="formData.supportFacility"
          placeholder="配套设施"
          :rows="4"
          show-count
          :maxlength="500"
        />
      </a-form-item>
      <a-form-item label="备注" name="remark" class="form-item-full">
        <a-textarea v-model:value="formData.remark" placeholder="备注" :rows="4" show-count :maxlength="500" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
import areaList from '@/json/region.json'
import { projectPage, queryBuilding } from '@/views/projects/apis.js'
import { queryFloor } from '@/views/building/apis/building.js'
import { getLeaseUnitTree } from '../apis/leaseUnitTree'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const emits = defineEmits(['assetChange', 'pcaCodeChange', 'projectChange'])

const formRef = ref()

const projectOptions = ref([])

/**
 * 联合验证省市区代码数组和详细地址
 * @param {any} _rule - 验证规则
 * @param {Array} value - 省市区代码数组
 * @returns {Promise} 验证结果
 */
const validateAddressInfo = () => {
  const pcaCodeArray = props.formData.pcaCodeArray
  const detailAddress = props.formData.detailAddress

  // 省市区验证
  if (!pcaCodeArray || !Array.isArray(pcaCodeArray) || pcaCodeArray.length === 0) {
    return Promise.reject('请选择完整的省市区信息')
  }
  if (pcaCodeArray.length < 3) {
    return Promise.reject('请选择到区级行政区')
  }

  // 详细地址验证
  if (!detailAddress || detailAddress.trim() === '') {
    return Promise.reject('请输入详细地址')
  }

  return Promise.resolve()
}

const rules = {
  name: [
    { required: true, message: '请输入单元名称', trigger: 'blur' },
    { min: 2, max: 50, message: '单元名称长度应为2-50个字符', trigger: 'blur' }
  ],
  pcaCodeArray: [{ required: true, validator: validateAddressInfo, trigger: 'change' }],
  detailAddress: [{ required: true, validator: validateAddressInfo, trigger: ['blur', 'change'] }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: 'change' }],
  ownerCompany: [{ required: true, message: '请选择资产权属公司', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  landNature: [{ required: true, message: '请选择使用权类型', trigger: 'change' }],
  treeId: [{ required: true, message: '请选择租赁单元分类', trigger: 'change' }],
  supportFacility: [{ max: 500, message: '配套设施描述不能超过500个字符', trigger: 'blur' }],
  remark: [{ max: 500, message: '备注不能超过500个字符', trigger: 'blur' }]
}

/**
 * 处理资产选择变更事件
 */
const handleAssetChange = (value) => {
  emits('assetChange', value)
}

/**
 * 处理项目选择变更事件
 */
const handleProjectChange = (value) => {
  emits('projectChange', value)
}

/**
 * 动态加载楼栋和楼层数据
 */
const loadBuildingFloorData = async (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true

  try {
    const isLoadingBuilding = selectedOptions.length === 1
    const api = isLoadingBuilding ? queryBuilding : queryFloor
    const res = await api({ id: targetOption.value })

    if (res?.result) {
      targetOption.children = res.result.map((item) => ({
        value: item.id,
        label: item.name,
        isLeaf: !isLoadingBuilding
      }))
    }
  } finally {
    targetOption.loading = false
  }
}

/**
 * 加载项目列表数据
 */
const loadProjectList = async () => {
  const { result } = await projectPage()
  projectOptions.value =
    result?.records?.map((item) => ({
      value: item.id,
      label: item.name,
      isLeaf: false
    })) || []

  if (props.formData.wyProject) {
    await initProjectData()
  }
}

/**
 * 初始化项目数据，用于编辑时回显级联选择器
 */
const initProjectData = async () => {
  const buildingRes = await queryBuilding({ id: props.formData.wyProject })
  const projectOption = projectOptions.value.find((item) => item.value === props.formData.wyProject)

  if (projectOption && buildingRes?.result) {
    projectOption.children = buildingRes.result.map((item) => ({
      value: item.id,
      label: item.name,
      isLeaf: false
    }))

    if (props.formData.wyBuilding) {
      const floorRes = await queryFloor({ id: props.formData.wyBuilding })
      const buildingOption = projectOption.children.find((item) => item.value === props.formData.wyBuilding)

      if (buildingOption && floorRes?.result) {
        buildingOption.children = floorRes.result.map((item) => ({
          value: item.id,
          label: item.name,
          isLeaf: true
        }))
      }
    }
  }
}

/**
 * 验证表单
 */
const validate = () => {
  return formRef.value?.validate()
}

/**
 * 重置表单字段
 */
const resetFields = () => {
  formRef.value?.resetFields()
}

watch(
  () => props.formData.virtualLeaseUnit,
  (newVal, oldVal) => {
    if (oldVal === true && newVal === false) {
      props.formData.houseOwner = undefined
    }
  }
)

onMounted(() => {
  loadProjectList()
})

defineExpose({
  validate,
  resetFields,
  initProjectData
})
</script>
