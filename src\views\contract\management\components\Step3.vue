<template>
  <div>
    <strong class="text-[16px]">合同账单明细预览</strong>
    <div class="my-[20px]">
      <a-radio-group v-model:value="paymentType" button-style="solid">
        <a-radio-button
          v-for="item in form.contractDetailBillsVOList"
          :key="item.paymentType"
          :value="item.paymentType"
        >
          {{ item.paymentType_dictText }}
        </a-radio-button>
      </a-radio-group>
    </div>
    <a-table
      :data-source="tableData"
      :columns="columns"
      :pagination="false"
      :scroll="{ y: 'calc(100vh - 400px)' }"
    ></a-table>
  </div>
</template>

<script setup>
const { form } = defineProps({
  form: { required: true, type: Object }
})

const paymentType = ref('全部')
const tableData = computed(() => {
  if (!(form.contractDetailBillsVOList && form.contractDetailBillsVOList.length)) return []
  const data = form.contractDetailBillsVOList.find((i) => i.paymentType === paymentType.value)
  return data ? data.contractDetailBillsList : []
})

const columns = [
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 100 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 100 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 100 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 100 },
  { title: '归属账期', dataIndex: 'incomeBelongYm', width: 100 }
]
</script>
