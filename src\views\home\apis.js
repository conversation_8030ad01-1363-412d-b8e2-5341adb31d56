import request from '@/apis/http'

// 数据看板-财务收款金额类型统计(万元)
export const recPaymentType = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/recPaymentType',
    params
  })
}

// 数据看板-应收逾期账龄统计(万元)
export const recOverdueAge = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/recOverdueAge',
    params
  })
}

// 数据看板-每月财务应收统计(万元)
export const recMonth = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/recMonth',
    params
  })
}

// 数据看板-资产总面积-资产数量(个)-本年资产应收(万元)-本年资产已收(万元)
export const houseOwnerData = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/houseOwnerData',
    params
  })
}

// 数据看板-资产面积状态统计(㎡)
export const houseOwnerAreaStatus = (params) => {
  return request({
    method: 'get',
    url: '/api/workbench/houseOwnerAreaStatus',
    params
  })
}
