// mock数据 - 消息模板列表
const mockTemplateList = [
  {
    id: 'bpm_chaoshi_tip',
    templateCode: 'bpm_chaoshi_tip',
    templateTitle: '流程办理超时提醒',
    templateContent: '超时提醒信息：您有待处理的超时任务，请尽快处理！',
    templateType: 'richText',
    isEnabled: true,
    createBy: '张三',
    createTime: '2024-02-01 19:01',
    updateBy: '李四',
    updateTime: '2024-03-15 10:30'
  },
  {
    id: 'contract_expire_tip',
    templateCode: 'contract_expire_tip',
    templateTitle: '流程办理超时提醒',
    templateContent: '超时提醒信息：您有待处理的超时任务，请尽快处理！',
    templateType: 'plainText',
    isEnabled: true,
    createBy: '王五',
    createTime: '2024-02-10 14:25',
    updateBy: '王五',
    updateTime: '2024-02-10 14:25'
  },
  {
    id: 'payment_reminder',
    templateCode: 'payment_reminder',
    templateTitle: '流程办理超时提醒',
    templateContent: '超时提醒信息：您有待处理的超时任务，请尽快处理！',
    templateType: 'plainText',
    isEnabled: true,
    createBy: '赵六',
    createTime: '2024-02-15 09:30',
    updateBy: '赵六',
    updateTime: '2024-02-15 09:30'
  },
  {
    id: 'maintenance_notice',
    templateCode: 'maintenance_notice',
    templateTitle: '流程办理超时提醒',
    templateContent: '超时提醒信息：您有待处理的超时任务，请尽快处理！',
    templateType: 'richText',
    isEnabled: false,
    createBy: '张三',
    createTime: '2024-03-01 16:45',
    updateBy: '李四',
    updateTime: '2024-03-02 10:15'
  },
  {
    id: 'welcome_message',
    templateCode: 'welcome_message',
    templateTitle: '流程办理超时提醒',
    templateContent: '超时提醒信息：您有待处理的超时任务，请尽快处理！',
    templateType: 'plainText',
    isEnabled: true,
    createBy: '王五',
    createTime: '2024-03-05 11:20',
    updateBy: '王五',
    updateTime: '2024-03-05 11:20'
  },
  {
    id: 'feedback_reply',
    templateCode: 'feedback_reply',
    templateTitle: '流程办理超时提醒',
    templateContent: '超时提醒信息：您有待处理的超时任务，请尽快处理！',
    templateType: 'plainText',
    isEnabled: true,
    createBy: '赵六',
    createTime: '2024-03-10 14:30',
    updateBy: '赵六',
    updateTime: '2024-03-10 14:30'
  }
]

// mock数据 - 消息模板详情
const mockTemplateDetail = {
  id: 'bpm_chaoshi_tip',
  templateCode: 'bpm_chaoshi_tip',
  templateTitle: '流程办理超时提醒',
  templateContent: '超时提醒信息：您有待处理的超时任务，请尽快处理！',
  templateType: 'richText',
  isEnabled: true,
  createBy: '张三',
  createTime: '2024-02-01 19:01',
  updateBy: '李四',
  updateTime: '2024-03-15 10:30'
}

// mock API函数
export const getTemplateList = (params) => {
  // 模拟分页
  const { pageNo = 1, pageSize = 10 } = params || {}
  const start = (pageNo - 1) * pageSize
  const end = start + pageSize

  // 模拟搜索
  let filteredList = [...mockTemplateList]
  if (params.templateTitle) {
    filteredList = filteredList.filter((item) => item.templateTitle.includes(params.templateTitle.replace(/\*/g, '')))
  }
  if (params.templateCode) {
    filteredList = filteredList.filter((item) => item.templateCode.includes(params.templateCode.replace(/\*/g, '')))
  }
  if (params.templateType) {
    filteredList = filteredList.filter((item) => item.templateType === params.templateType)
  }
  if (params.isEnabled) {
    filteredList = filteredList.filter((item) => item.isEnabled === params.isEnabled)
  }

  const records = filteredList.slice(start, end)

  return Promise.resolve({
    success: true,
    message: '',
    code: 0,
    result: {
      total: filteredList.length,
      current: pageNo,
      records,
      pages: Math.ceil(filteredList.length / pageSize),
      size: pageSize
    },
    timestamp: Date.now()
  })
}

export const getTemplateById = (params) => {
  const { id } = params
  const template = mockTemplateList.find((item) => item.id === id) || { ...mockTemplateDetail }

  return Promise.resolve({
    code: 200,
    success: true,
    data: template,
    message: ''
  })
}

export const addTemplate = (data) => {
  // 模拟添加操作
  const newTemplate = {
    ...data,
    id: data.templateCode,
    createBy: '当前用户',
    createTime: new Date().toLocaleString(),
    updateBy: '当前用户',
    updateTime: new Date().toLocaleString()
  }

  mockTemplateList.unshift(newTemplate)

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '创建成功'
  })
}

export const updateTemplate = (data) => {
  // 模拟更新操作
  const index = mockTemplateList.findIndex((item) => item.id === data.id)
  if (index !== -1) {
    mockTemplateList[index] = {
      ...mockTemplateList[index],
      ...data,
      updateBy: '当前用户',
      updateTime: new Date().toLocaleString()
    }
  }

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '更新成功'
  })
}

export const deleteTemplate = (params) => {
  // 模拟删除操作
  const index = mockTemplateList.findIndex((item) => item.id === params.id)
  if (index !== -1) {
    mockTemplateList.splice(index, 1)
  }

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '删除成功'
  })
}

export const batchDeleteTemplate = (params) => {
  // 模拟批量删除操作
  const ids = params.ids.split(',')
  ids.forEach((id) => {
    const index = mockTemplateList.findIndex((item) => item.id === id)
    if (index !== -1) {
      mockTemplateList.splice(index, 1)
    }
  })

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '批量删除成功'
  })
}

export const updateStatus = (data) => {
  // 模拟更新状态操作
  const ids = data.ids.split(',')
  ids.forEach((id) => {
    const template = mockTemplateList.find((item) => item.id === id)
    if (template) {
      template.isEnabled = data.isEnabled
      template.updateBy = '当前用户'
      template.updateTime = new Date().toLocaleString()
    }
  })

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '状态更新成功'
  })
}
