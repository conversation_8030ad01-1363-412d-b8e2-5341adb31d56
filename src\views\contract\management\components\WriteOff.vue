<template>
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <a-button>
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <s-input v-model="keyword"></s-input>
    </div>
    <columns-set ref="columnsRef" :default-columns="defaultColumns"></columns-set>
  </div>
  <a-table :data-source="list" :columns="columns" :loading="tableLoading" :pagination="false" row-key="id"></a-table>
</template>

<script setup>
const defaultColumns = [
  { title: '单据id', dataIndex: 'leaseUnit_dictText', fixed: 'left' },
  { title: '租赁单元', dataIndex: 'originalRent', width: 200 },
  { title: '款项类型', dataIndex: 'detailAddress' },
  { title: '期数/总期数', dataIndex: 'collectionCompany_dictText' },
  { title: '归属账期', dataIndex: 'ownerCompany_dictText' },
  { title: '核销金额', dataIndex: 'areaManager_dictText' },
  { title: '核销时间', dataIndex: 'leaseArea' },
  { title: '核销人', dataIndex: 'leaseUse_dictText' }
]
const columnsRef = ref()
const columns = computed(() => columnsRef.value?.columns)
const tableLoading = ref(false)
const list = ref([])

const keyword = ref('')
</script>
