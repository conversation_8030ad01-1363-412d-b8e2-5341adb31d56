<template>
  <div>
    <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">财务信息</h4>
    <a-form ref="financeFormRef" :model="formData" :rules="financeRules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="开户行" name="depositBank">
        <a-input v-model:value="formData.depositBank" placeholder="请输入开户行" />
      </a-form-item>
      <a-form-item label="开户行账号" name="depositBankAccount">
        <a-input v-model:value="formData.depositBankAccount" placeholder="请输入开户行账号" />
      </a-form-item>
      <a-form-item label="发票名称" name="invoiceName">
        <a-input v-model:value="formData.invoiceName" placeholder="请输入发票名称" />
      </a-form-item>
      <a-form-item label="发票类型" name="invoiceType">
        <dict-select
          v-model="formData.invoiceType"
          placeholder="发票类型"
          code="CT_BASE_ENUM_Customer_InvoiceType"
        ></dict-select>
      </a-form-item>
      <a-form-item label="税率(%)" name="taxRate">
        <a-input-number v-model:value="formData.taxRate" style="width: 100%" addon-after="%" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const financeFormRef = ref()

const financeRules = {
  taxRate: [{ type: 'number', min: 0, message: '税率不能为负数', trigger: 'blur' }]
}

/**
 * 验证财务信息表单
 */
const validate = async () => {
  await financeFormRef.value?.validate()
}

/**
 * 重置表单
 */
const resetFields = () => {
  financeFormRef.value?.resetFields()
}

defineExpose({
  validate,
  resetFields
})
</script>
