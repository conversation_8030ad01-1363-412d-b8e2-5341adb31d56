import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/dataFileFillDetail/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/dataFileFillDetail/queryById',
    params
  })
}

export const fileDetail = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/dataFileFillDetail/queryDataFileFillDetailEntryByMainId',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/dataFileFillDetail/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/dataFileFillDetail/importExcel',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/dataFileFillDetail/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/dataFileFillDetail/unAudit',
    data
  })
}

export const save = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/dataFileFillDetail/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/dataFileFillDetail/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/dataFileFillDetail/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/contractmanage/dataFileFillDetail/deleteBatch',
    params
  })
}
