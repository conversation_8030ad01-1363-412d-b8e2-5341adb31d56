import request from '@/apis/http'

export const getMenuList = (params) => {
  return request({
    method: 'get',
    url: '/sys/permission/list',
    params
  })
}

export const addMenu = (data) => {
  return request({
    method: 'post',
    url: '/sys/permission/add',
    data
  })
}

export const editMenu = (data) => {
  return request({
    method: 'post',
    url: '/sys/permission/edit',
    data
  })
}

export const deleteMenu = (params) => {
  return request({
    method: 'delete',
    url: '/sys/permission/delete',
    params
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/sys/permission/deleteBatch',
    params
  })
}

// 检查访问地址url是否可用
export const checkUrl = (params) => {
  return request({
    method: 'get',
    url: '/sys/permission/checkPermDuplication',
    params
  })
}

// 查询默认首页配置
export const queryDefaultIndex = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysRoleIndex/queryDefIndex',
    params
  })
}
