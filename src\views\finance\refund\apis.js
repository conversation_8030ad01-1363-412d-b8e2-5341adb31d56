import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 编辑退款申请单
export function editRefundReqBill(data) {
  return request({
    url: '/biz/funds/refundReqBill/edit',
    method: 'post',
    data
  })
}

// 反审核退款申请单
export function unAuditRefundReqBill(data) {
  return request({
    url: '/biz/funds/refundReqBill/unAudit',
    method: 'post',
    data
  })
}

// 提交退款申请单
export function submitRefundReqBill(data) {
  return request({
    url: '/biz/funds/refundReqBill/submit',
    method: 'post',
    data
  })
}

// 审核退款申请单
export function auditRefundReqBill(data) {
  return request({
    url: '/biz/funds/refundReqBill/audit',
    method: 'post',
    data
  })
}

// 添加退款申请单
export function addRefundReqBill(data) {
  return request({
    url: '/biz/funds/refundReqBill/add',
    method: 'post',
    data
  })
}

// 查询退款申请单分录
export function queryRefundReqBillEntries(params) {
  return request({
    url: '/biz/funds/refundReqBill/queryRefundReqBillEntryByMainId',
    method: 'get',
    params
  })
}

// 根据ID查询退款申请单
export const getRefundReqBillById = (params) => {
  return request({
    method: 'get',
    url: '/biz/funds/refundReqBill/queryById',
    params
  })
}

// 获取退款申请单列表
export function getRefundReqBillList(params) {
  return request({
    url: '/biz/funds/refundReqBill/list',
    method: 'get',
    params
  })
}

// 删除退款申请单
export function deleteRefundReqBill(params) {
  return request({
    url: '/biz/funds/refundReqBill/delete',
    method: 'delete',
    params
  })
}

// 批量删除退款申请单
export function batchDeleteRefundReqBill(params) {
  return request({
    url: '/biz/funds/refundReqBill/deleteBatch',
    method: 'delete',
    params
  })
}

// 退款申请单-选择退款明细列表
export function f7RefundDetailList(params) {
  return request({
    url: '/biz/funds/refundReqBill/f7RefundDetailList',
    method: 'get',
    params
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/funds/refundReqBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/funds/refundReqBill/importExcel', data, controller)
}
