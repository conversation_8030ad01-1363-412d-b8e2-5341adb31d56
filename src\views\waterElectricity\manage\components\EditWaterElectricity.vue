<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}水电表`"
    class="edit-water-electricity-drawer common-drawer"
    width="1072px"
    placement="right"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="编码(表号)" name="number">
          <a-input v-model:value="formData.number" placeholder="请输入编码(表号)" />
        </a-form-item>
        <a-form-item label="名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入名称" show-count :maxlength="50" />
        </a-form-item>
        <a-form-item label="倍率" name="doubleRate">
          <a-input-number
            v-model:value="formData.doubleRate"
            placeholder="请输入倍率"
            style="width: 100%"
            :min="0"
            :precision="2"
          />
        </a-form-item>
        <a-form-item label="类型" name="type">
          <dict-select
            v-model="formData.type"
            placeholder="请选择类型"
            code="CT_BASE_ENUM_WaterElectriCityTableNum_Type"
          ></dict-select>
        </a-form-item>
        <a-form-item label="属性" name="property">
          <dict-select
            v-model="formData.property"
            placeholder="请选择属性"
            code="CT_BASE_ENUM_WaterElectriCityTableNum_Property"
          ></dict-select>
        </a-form-item>
        <a-form-item label="分组" name="treeId">
          <api-tree-select
            v-model="formData.treeId"
            style="width: 100%"
            :async-fn="getWaterElectricityTableNumTree"
            placeholder="请选择分组"
          />
        </a-form-item>
        <a-form-item label="地址" name="pcaCodeArray">
          <div class="flex">
            <a-cascader
              class="!mr-[4px]"
              v-model:value="formData.pcaCodeArray"
              :options="areaList"
              placeholder="请选择省市区"
            />
            <a-input v-model:value="formData.address" placeholder="请输入详细地址" style="width: 100%" />
          </div>
        </a-form-item>
        <a-form-item label="租金归集公司" name="collectionCompany">
          <dept-tree-select
            v-model="formData.collectionCompany"
            placeholder="请选择租金归集公司"
            type="company"
          ></dept-tree-select>
        </a-form-item>
        <a-form-item label="资产权属公司" name="ownerCompany">
          <dept-tree-select
            v-model="formData.ownerCompany"
            placeholder="请选择资产权属公司"
            type="company"
          ></dept-tree-select>
        </a-form-item>
        <a-form-item label="物业管理公司" name="manageCompany">
          <dept-tree-select
            v-model="formData.manageCompany"
            placeholder="请选择物业管理公司"
            type="company"
          ></dept-tree-select>
        </a-form-item>
        <a-form-item label="单价" name="price">
          <a-input-number
            v-model:value="formData.price"
            placeholder="请输入单价"
            style="width: 100%"
            :min="0"
            :precision="2"
            addon-after="元"
          />
        </a-form-item>
        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注" :rows="4" show-count :maxlength="500" />
        </a-form-item>
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">确认</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import areaList from '@/json/region.json'
import { addWaterElectricity, editWaterElectricity } from '../apis/waterElectricity'
import { getWaterElectricityTableNumTree } from '../apis/waterElectricityTableNumTree'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  id: undefined,
  number: undefined,
  name: undefined,
  status: 'ENABLE',
  doubleRate: undefined,
  type: undefined,
  property: undefined,
  treeId: undefined,
  address: undefined,
  price: undefined,
  collectionCompany: undefined,
  ownerCompany: undefined,
  manageCompany: undefined,
  remark: undefined,
  pcaCode: undefined,
  pcaCodeArray: []
}

const formData = reactive({ ...formDataDefault })

/**
 * 联合验证省市区代码数组和详细地址
 * @param {any} _rule - 验证规则
 * @param {Array} value - 省市区代码数组
 * @returns {Promise} 验证结果
 */
const validateAddressInfo = () => {
  const pcaCodeArray = formData.pcaCodeArray
  const address = formData.address

  // 省市区验证
  if (!pcaCodeArray || !Array.isArray(pcaCodeArray) || pcaCodeArray.length === 0) {
    return Promise.reject('请选择完整的省市区信息')
  }
  if (pcaCodeArray.length < 3) {
    return Promise.reject('请选择到区级行政区')
  }

  // 详细地址验证
  if (!address || address.trim() === '') {
    return Promise.reject('请输入详细地址')
  }

  return Promise.resolve()
}

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  number: [{ required: true, message: '请输入编码(表号)', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  doubleRate: [{ type: 'number', min: 0.01, message: '倍率必须大于0', trigger: 'blur' }],
  pcaCodeArray: [{ required: true, validator: validateAddressInfo, trigger: 'change' }],
  address: [{ required: true, validator: validateAddressInfo, trigger: ['blur', 'change'] }],
  collectionCompany: [{ required: true, message: '请选择租金归集公司', trigger: 'change' }],
  ownerCompany: [{ required: true, message: '请选择资产权属公司', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  price: [{ type: 'number', min: 0.01, message: '单价必须大于0', trigger: 'blur' }]
}

/**
 * 打开抽屉弹窗
 * @param {Object} record 水电表记录，编辑时传入
 */
const open = (record) => {
  visible.value = true
  confirmLoading.value = true

  if (record?.id) {
    Object.assign(formData, record)
    // 处理省市区数据
    if (formData.pcaCode && typeof formData.pcaCode === 'string') {
      formData.pcaCodeArray = formData.pcaCode.split(',')
    }
  }
  confirmLoading.value = false
}

/**
 * 取消编辑并关闭抽屉
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  visible.value = false
  emits('refresh')
}

/**
 * 保存水电表信息
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true
  try {
    formData.pcaCode = formData.pcaCodeArray.join(',')
    if (formData.id) {
      await editWaterElectricity(formData)
    } else {
      await addWaterElectricity(formData)
    }
  } finally {
    confirmLoading.value = false
  }
  message.success('保存成功')
  handleCancel()
}

defineExpose({ open })
</script>

<style lang="less">
.edit-water-electricity-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }
}
</style>
