import request from './http'

export const login = (data) => {
  return request({
    method: 'POST',
    url: '/sys/login',
    data
  })
}

export const queryPermissions = () => {
  return request({
    method: 'get',
    url: `/sys/permission/getUserPermissionByToken?_t=${Date.now()}`
  })
}

export const getCaptchaImg = (checkKey) => {
  return request({
    method: 'get',
    url: `/sys/randomImage/${checkKey}`
  })
}

export const logout = () => {
  return request({
    method: 'get',
    url: `/sys/logout?t=${Date.now()}`
  })
}

export const updatePassword = (data) => {
  return request({
    method: 'put',
    url: '/sys/user/updatePassword',
    data
  })
}
