<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 || dataList.length === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="delete" @click="handleDelete">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">退款详情</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_AuditStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">业务日期：{{ detailData.bizDate || '-' }}</span>
        <span class="w-[50%]">退款客户：{{ detailData.customer_dictText || '-' }}</span>
        <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
        <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
        <span class="w-[50%]">审核人：{{ detailData.auditBy_dictText || '-' }}</span>
        <span class="w-[50%]">审核时间：{{ detailData.auditTime || '-' }}</span>
        <span class="w-[50%]">退款时间：{{ detailData.refundTime || '-' }}</span>
        <span class="w-[50%]">退款金额：{{ detailData.refundReqAmount || '-' }}</span>
        <span class="w-[100%]">备注：{{ detailData.remark || '-' }}</span>
      </div>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">退款明细</h4>
      <a-table
        v-if="list?.length"
        :columns="refundColumns"
        :data-source="list"
        :pagination="false"
        :scroll="{ x: 1200, y: tableHeight }"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <a-button type="link" danger @click="handleRemoveRefundItem(record)">删除</a-button>
          </template>
        </template>
      </a-table>
      <div v-else class="flex flex-col items-center">
        <img src="@/assets/imgs/no-data.png" />
        <span class="text-tertiary">暂无数据</span>
      </div>
    </a-spin>
  </a-drawer>
  <edit-refund ref="editDrawerRef" @refresh="refreshData" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import EditRefund from './EditRefund.vue'
import { getRefundReqBillById, queryRefundReqBillEntries, deleteRefundReqBill } from '../apis'

const props = defineProps({
  dataList: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['refresh', 'edit'])

const { list, onTableFetch, tableHeight } = usePageTable(queryRefundReqBillEntries)

const visible = ref(false)
const loading = ref(false)
const editDrawerRef = ref()
const detailData = ref({})
const entries = ref([])

const currentIndex = computed(() => {
  return props.dataList.findIndex((i) => i.id === detailData.value.id)
})

const refundColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract', width: 160, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueTransferAmount', width: 120, fixed: 'right' },
  { title: '本次转款金额', dataIndex: 'thisTransferOutAmount', width: 120, fixed: 'right' },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true, fixed: 'right' }
]

/**
 * 打开退款详情抽屉
 * @param {Object} record - 退款记录对象
 */
const open = async (record) => {
  visible.value = true
  await loadDetail(record.id)
}

/**
 * 关闭抽屉并重置数据
 */
const handleClose = () => {
  visible.value = false
  detailData.value = {}
  entries.value = []
}

/**
 * 编辑当前退款记录
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 删除当前退款记录
 */
const handleDelete = async () => {
  try {
    await deleteRefundReqBill({ id: detailData.value.id })
    message.success('删除成功')
    handleClose()
    emits('refresh')
  } catch {
    message.error('删除失败，请重试')
  }
}

/**
 * 删除退款明细项
 * @param {Object} record - 要删除的明细项记录
 */
const handleRemoveRefundItem = (record) => {
  const index = list.value.findIndex((item) => item.id === record.id)
  if (index > -1) {
    list.value.splice(index, 1)
    message.success('删除成功')
  }
}

/**
 * 切换到指定索引的退款记录
 * @param {number} index - 目标记录的索引
 */
const handleSwitchDetail = async (index) => {
  const record = props.dataList[index]
  if (record) {
    await loadDetail(record.id)
  }
}

/**
 * 刷新详情数据
 */
const refreshData = () => {
  loadDetail(detailData.value.id)
  emits('refresh')
}

/**
 * 加载退款详情数据
 * @param {string} id - 退款记录ID
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const { result } = await getRefundReqBillById({ id })
    detailData.value = result
    onTableFetch({ id })
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>
