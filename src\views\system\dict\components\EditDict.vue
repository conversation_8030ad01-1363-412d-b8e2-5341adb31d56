<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑字典' : '新增字典'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '100px' } }" autocomplete="off">
      <a-form-item label="字典名称" name="dictName">
        <a-input v-model:value="form.dictName" placeholder="请输入字典名称" :maxlength="10" />
      </a-form-item>
      <a-form-item label="字典编码" name="dictCode">
        <a-input
          v-model:value="form.dictCode"
          placeholder="请输入字典编码"
          :maxlength="10"
          :disabled="Boolean(form.id)"
        />
      </a-form-item>
      <a-form-item label="字典描述" name="description">
        <a-textarea v-model:value="form.description" placeholder="请输入字典描述" :maxlength="200" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addDict, editDict, checkDict } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  description: '',
  dictCode: '',
  dictName: '',
  id: ''
})

const validateCode = async (rule, value) => {
  if (!value) return Promise.reject(new Error('请输入字典编码'))
  try {
    await checkDict('sys_dict', 'dict_code', form.dictCode)
  } catch (error) {
    return Promise.reject(error.message)
  }
}

const rules = {
  dictName: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
  dictCode: [{ required: true, validator: validateCode, trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await editDict(form) : await addDict(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.description = ''
  form.dictCode = ''
  form.dictName = ''
  form.id = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
