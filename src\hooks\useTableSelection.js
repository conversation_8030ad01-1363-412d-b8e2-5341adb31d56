/**
 * ant-design-vue a-table组件的选择功能，支持单选、多选和翻页
 * @param {Array} tableData 表格数据
 * @param {String} rowKey 表格每条数据的唯一值
 * @param {Boolean} isSingle 是否是单选模式，默认为false（多选模式）
 * @param {Boolean} isTreeData 是否是树形数据，如果是树形数据，则一般是不分页的，就不需要考虑分页的情况
 */
const useTableSelection = (tableData, rowKey = 'id', isSingle = false, isTreeData = false) => {
  const selectedRowKeys = ref([])
  const selectedRows = ref([])

  // rows: 选中的数据
  const onSelectChange = (_, rows) => {
    if (isTreeData) {
      selectedRowKeys.value = rows.map((item) => item[rowKey])
      selectedRows.value = rows
      return
    }

    if (isSingle) {
      // 单选模式：只保留最新选中的一项
      if (rows.length > 0) {
        const latestRow = rows[rows.length - 1]
        selectedRowKeys.value = [latestRow[rowKey]]
        selectedRows.value = [latestRow]
      } else {
        selectedRowKeys.value = []
        selectedRows.value = []
      }
      return
    }

    // 多选模式：原有的跨页选择逻辑
    const ids = tableData.value.map((item) => item[rowKey]) // 本页全部数据的id列表
    rows.forEach((row) => {
      const index = ids.indexOf(row[rowKey])
      if (index !== -1) {
        ids.splice(index, 1) // 将本页被选中的数据，从ids里剔除
      }
    })
    // 此时的ids=本页未被选中的数据

    // 遍历rows，如果selectedRows不存在，就追加
    rows.forEach((row) => {
      const index = selectedRows.value.findIndex((i) => i[rowKey] === row[rowKey])
      if (index === -1) {
        selectedRows.value.push(row)
      }
    })

    // 遍历本页未被选中的数据，如果selectedRows存在，就删掉
    ids.forEach((id) => {
      const index = selectedRows.value.findIndex((i) => i[rowKey] === id)
      if (index !== -1) {
        selectedRows.value.splice(index, 1)
      }
    })

    selectedRowKeys.value = selectedRows.value.map((item) => item[rowKey])
  }

  const clearSelection = () => {
    selectedRowKeys.value = []
    selectedRows.value = []
  }

  return {
    onSelectChange,
    clearSelection,
    selectedRowKeys,
    selectedRows
  }
}

export default useTableSelection
