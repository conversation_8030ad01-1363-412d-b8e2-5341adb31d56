// mock数据 - 消息发送列表
const mockMessageList = [
  {
    id: 'msg_001',
    title: '合同到期提醒',
    content: '尊敬的客户，您的租赁合同将于2024年6月30日到期，请提前做好续租或退租准备。如有疑问，请联系客服。',
    recipient: '张三、李四、王五（共3人）',
    count: 1,
    status: '发送成功',
    sendTime: '2024-05-20 10:30:00',
    sendWay: '短信',
    readFlag: '2/3已读'
  },
  {
    id: 'msg_002',
    title: '缴费通知',
    content: '您好，本月租金及物业费共计5000元，请于本月25日前完成缴费。逾期将产生滞纳金。',
    recipient: '赵六、孙七、周八、吴九（共4人）',
    count: 2,
    status: '部分失败',
    sendTime: '2024-05-19 09:15:00',
    sendWay: '微信',
    readFlag: '3/4已读'
  },
  {
    id: 'msg_003',
    title: '维修通知',
    content: '因电梯维护保养，明日上午9:00-12:00期间电梯将暂停使用，请各位业主合理安排出行时间。',
    recipient: '全体业主（共50人）',
    count: 1,
    status: '发送成功',
    sendTime: '2024-05-18 16:45:00',
    sendWay: '短信+微信',
    readFlag: '45/50已读'
  },
  {
    id: 'msg_004',
    title: '会议邀请',
    content: '业主大会定于本周六下午2点在物业会议室召开，讨论小区绿化改造方案，请各位业主代表准时参加。',
    recipient: '业主代表（共12人）',
    count: 1,
    status: '发送中',
    sendTime: '2024-05-17 14:20:00',
    sendWay: '邮件',
    readFlag: '8/12已读'
  },
  {
    id: 'msg_005',
    title: '停水停电通知',
    content: '因市政管网维修，明日8:00-18:00将停水停电，请提前做好准备。恢复时间另行通知。',
    recipient: '全体住户（共120人）',
    count: 3,
    status: '发送失败',
    sendTime: '2024-05-16 11:30:00',
    sendWay: '短信',
    readFlag: '0/120已读'
  },
  {
    id: 'msg_006',
    title: '活动通知',
    content: '六一儿童节社区将举办亲子活动，时间为6月1日上午9:00，地点在小区中心广场，欢迎带孩子参加。',
    recipient: '有孩子的家庭（共25人）',
    count: 1,
    status: '发送成功',
    sendTime: '2024-05-15 08:45:00',
    sendWay: '微信',
    readFlag: '20/25已读'
  },
  {
    id: 'msg_007',
    title: '安全提醒',
    content: '近期发现有陌生人员在小区内出入，请各位业主注意安全，遇到可疑人员及时联系保安。',
    recipient: '全体业主（共50人）',
    count: 1,
    status: '发送成功',
    sendTime: '2024-05-14 20:15:00',
    sendWay: '短信+微信',
    readFlag: '48/50已读'
  },
  {
    id: 'msg_008',
    title: '收费标准调整通知',
    content: '根据物价局批复，自下月起物业管理费将调整为每平方米3.5元，具体详情请查看公告栏。',
    recipient: '全体业主（共50人）',
    count: 2,
    status: '发送成功',
    sendTime: '2024-05-13 15:00:00',
    sendWay: '邮件',
    readFlag: '42/50已读'
  },
  {
    id: 'msg_009',
    title: '装修申请提醒',
    content: '您提交的装修申请已通过审核，请于装修前到物业办公室办理相关手续并缴纳装修保证金。',
    recipient: '陈十（1人）',
    count: 1,
    status: '发送成功',
    sendTime: '2024-05-12 13:20:00',
    sendWay: '短信',
    readFlag: '1/1已读'
  },
  {
    id: 'msg_010',
    title: '车位租赁到期提醒',
    content: '您租赁的车位A101将于本月底到期，如需续租请及时到物业办公室办理手续。',
    recipient: '林十一（1人）',
    count: 1,
    status: '发送成功',
    sendTime: '2024-05-11 10:10:00',
    sendWay: '微信',
    readFlag: '1/1已读'
  },
  {
    id: 'msg_011',
    title: '系统维护通知',
    content: '物业管理系统将于今晚23:00-次日凌晨1:00进行维护升级，期间暂停线上服务，请谅解。',
    recipient: '全体用户（共200人）',
    count: 1,
    status: '定时发送',
    sendTime: '2024-05-24 22:00:00',
    sendWay: '短信+微信',
    readFlag: '0/200已读'
  },
  {
    id: 'msg_012',
    title: '节假日值班安排',
    content: '端午节期间物业办公室值班时间调整为9:00-17:00，紧急情况请拨打24小时值班电话。',
    recipient: '全体业主（共50人）',
    count: 1,
    status: '草稿',
    sendTime: '',
    sendWay: '短信',
    readFlag: '0/50已读'
  }
]

/**
 * 获取消息发送列表
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export const getMessageList = (params) => {
  // 模拟分页
  const { pageNo = 1, pageSize = 10 } = params || {}
  const start = (pageNo - 1) * pageSize
  const end = start + pageSize
  // 模拟搜索
  let filteredList = [...mockMessageList]
  if (params.title) {
    filteredList = filteredList.filter((item) => item.title.includes(params.title.replace(/\*/g, '')))
  }
  if (params.status) {
    filteredList = filteredList.filter((item) => item.status === params.status)
  }
  if (params.sendWay) {
    filteredList = filteredList.filter((item) => item.sendWay.includes(params.sendWay))
  }
  if (params.recipient) {
    filteredList = filteredList.filter((item) => item.recipient.includes(params.recipient.replace(/\*/g, '')))
  }

  const records = filteredList.slice(start, end)

  return Promise.resolve({
    success: true,
    message: '查询成功',
    code: 200,
    result: {
      total: filteredList.length,
      current: pageNo,
      size: pageSize,
      pages: Math.ceil(filteredList.length / pageSize),
      records
    }
  })
}

/**
 * 删除消息记录
 * @param {string} id - 消息ID
 * @returns {Promise} API响应
 */
export const deleteMessage = (id) => {
  const index = mockMessageList.findIndex((item) => item.id === id)
  if (index !== -1) {
    mockMessageList.splice(index, 1)
  }

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '删除成功'
  })
}

/**
 * 批量删除消息记录
 * @param {Array} ids - 消息ID数组
 * @returns {Promise} API响应
 */
export const batchDeleteMessages = (ids) => {
  ids.forEach((id) => {
    const index = mockMessageList.findIndex((item) => item.id === id)
    if (index !== -1) {
      mockMessageList.splice(index, 1)
    }
  })

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '批量删除成功'
  })
}

/**
 * 重新发送消息
 * @param {string} id - 消息ID
 * @returns {Promise} API响应
 */
export const resendMessage = (id) => {
  const message = mockMessageList.find((item) => item.id === id)
  if (message) {
    message.count += 1
    message.status = '发送成功'
    message.sendTime = new Date()
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
      .replace(/\//g, '-')
  }

  return Promise.resolve({
    code: 200,
    success: true,
    data: true,
    message: '重新发送成功'
  })
}

/**
 * 获取消息详情
 * @param {string} id - 消息ID
 * @returns {Promise} API响应
 */
export const getMessageById = (id) => {
  const message = mockMessageList.find((item) => item.id === id)

  if (!message) {
    return Promise.resolve({
      success: false,
      message: '消息不存在',
      code: 404,
      result: null
    })
  }

  // 构造详情数据，添加更多详细信息
  const detailData = {
    ...message,
    creator: '系统管理员', // 发送人
    createTime: message.sendTime, // 创建时间使用发送时间
    templateContent: message.content, // 内容字段映射
    readFlag: message.readFlag // 已读状态
  }

  return Promise.resolve({
    success: true,
    message: '获取成功',
    code: 200,
    result: detailData
  })
}
