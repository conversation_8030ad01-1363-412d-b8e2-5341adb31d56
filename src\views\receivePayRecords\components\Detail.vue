<template>
  <a-drawer
    v-model:open="visible"
    :mask-closable="false"
    class="detail-header-drawer common-drawer"
    placement="right"
    width="1072px"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === 0 }"
            @click="handleSwitch(1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === ids.length - 1 }"
            @click="handleSwitch()"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn">核销</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="turnToPage">核销记录</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="handleDel">删除</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="handleUnAudit">反审批</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <!-- ：{{ detailData.name }} -->
        <h2 class="text-[18px] font-bold mr-[12px]">收付款记录</h2>
        <a-tag :type="getStatusColor(detailData.status)">
          {{ detailData.status_dictText }}
        </a-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <!-- 锚点导航 -->
      <anchor-tabs :tab-list="navList" height="calc(100vh - 295px)">
        <template #baseInfo>
          <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">实际金额：{{ detailData.actualReceiveAmt || '-' }}</span>
            <span class="w-[50%]">手续费：{{ detailData.serviceCharge || '-' }}</span>
            <span class="w-[50%]">收款公司：{{ detailData.manageCompany_dictText || '-' }}</span>
            <span class="w-[50%]">发生时间：{{ detailData.receiveDate || '-' }}</span>
            <span class="w-[50%]">所属客户：{{ detailData.customer || '-' }}</span>
            <span class="w-[50%]">实际来款人：{{ detailData.name || '-' }}</span>
            <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
            <span class="w-[50%]">业务部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
            <span class="w-[50%]">业务状态：{{ detailData.name || '-' }}</span>
            <span class="w-[50%]">已核销金额：{{ detailData.name || '-' }}</span>
            <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
          </div>
        </template>
        <template #writeoffInfo>
          <div class="flex justify-between items-center mb-[12px]">
            <h2 class="text-[16px] font-bold">对应核销明细</h2>
            <div>
              <span class="mr-[10px]">剩余待核销：5434</span>
              <a-button type="primary">继续核销</a-button>
            </div>
          </div>
          <a-table :data-source="[]" :columns="columns" :scroll="{ y: 300, x: 2000 }" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="rowView(record)">应收详情</span>
              </template>
            </template>
          </a-table>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
</template>
<script setup>
import AddEdit from './AddEdit.vue'
import { delById, detailById, unAudit } from '../apis'
import { Modal, message } from 'ant-design-vue'
const emits = defineEmits(['loadData'])
const { ids } = defineProps({
  ids: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const curIndex = ref(0)
const visible = ref(false)
// 导航项
const navList = [
  { name: 'baseInfo', title: '基础信息', showTitle: false },
  { name: 'writeoffInfo', title: '核销明细', showTitle: false }
]
const columns = [
  { title: '单据ID', dataIndex: 'number', width: 150, fixed: true },
  { title: '客户名称', dataIndex: 'number', width: 150 },
  { title: '合同', dataIndex: 'number', width: 150 },
  { title: '租赁单元', dataIndex: 'number', width: 150 },
  { title: '款项类型', dataIndex: 'number', width: 150 },
  { title: '期数/总期数', dataIndex: 'number', width: 150 },
  { title: '归属账期', dataIndex: 'number', width: 150 },
  { title: '核销金额', dataIndex: 'number', width: 150 },
  { title: '核销时间', dataIndex: 'number', width: 150 },
  { title: '核销人', dataIndex: 'number', width: 150 },
  { title: '应收账单', dataIndex: 'number', width: 150 },
  { title: '应收开始时间', dataIndex: 'number', width: 150 },
  { title: '应收结束时间', dataIndex: 'number', width: 150 },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    const activeIndex = ids.indexOf(data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    getDetailById(data.id)
  }
}
defineExpose({ open })
// 通过id获取详情
const detailData = ref({})
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  detailData.value = result
}
const handleSwitch = (type) => {
  // 上一条
  if (type) {
    if (curIndex.value > 0) {
      curIndex.value--
      getDetailById(ids[curIndex.value])
    }
    return
  }
  if (curIndex.value < ids.length - 1) {
    curIndex.value++
    getDetailById(ids[curIndex.value])
  }
}
const loading = ref(false)
// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    AUDITOK: 'success', // 审核通过
    AUDITING: 'processing', // 审核中
    TEMP: 'default', // 暂存
    AUDITNO: 'error', // 审核不通过
    BACK: 'warning', // 已撤回
    ENABLE: 'success', // 启用
    DISABLE: 'error', // 禁用
    CLOSED: 'default', // 关闭
    INTEND: 'blue' // 意向
  }
  return statusMap[status] || 'default'
}
// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '提示',
    content: '确认删除当前收付款记录？',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      visible.value = false
    }
  })
}
// 反审批
const handleUnAudit = () => {
  Modal.confirm({
    title: '提示',
    content: '确认反审核当前收付款记录？',
    centered: true,
    onOk: async () => {
      const { result } = await unAudit(detailData.value)
      message.success(result)
      emits('loadData')
      // visible.value = false
    }
  })
}
// 查看核销记录
const router = useRouter()
const turnToPage = () => {
  return router.push({ path: '/writeOff/records', query: {} })
}
const receivableDetailRef = ref()
// 查看
const rowView = (row) => {
  receivableDetailRef?.value.open(row)
}
</script>
<style lang="less">
.detail-header-drawer {
  .ant-drawer-header {
    width: 100%;
    padding-right: 58px;
    font-size: 14px;
  }
  .ant-drawer-close {
    margin-inline-end: 0;
  }
  .ant-drawer-header-title {
    flex: 0;
  }
  .ant-drawer-extra {
    flex: 1;
  }
}
</style>
