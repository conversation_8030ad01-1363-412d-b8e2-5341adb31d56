<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>

    <a-tabs class="!mt-[12px]" v-model:active-key="activeTab">
      <a-tab-pane key="1" tab="已到期"></a-tab-pane>
      <a-tab-pane key="2" tab="未到期"></a-tab-pane>
    </a-tabs>
    <div class="flex justify-between !my-[24px]">
      <div class="flex">
        <a-button @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form class="!ml-[40px]" autocomplete="off" layout="inline">
          <a-form-item label="结算状态" v-if="activeTab === '1'">
            <a-select class="!w-[280px]" v-model:value="search.treeId" placeholder="请选择类别"></a-select>
          </a-form-item>
          <a-form-item label="搜索">
            <s-input class="!w-[280px]" v-model:value="search.treeId" placeholder="请搜索"></s-input>
          </a-form-item>
          <a-form-item>
            <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
          </a-form-item>
        </a-form>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowView(record)">详情</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn">新增核销</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn">查看合同</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="turnToPage(record)">核销记录</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn">调整金额</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <!-- 详情 -->
    <detail ref="detailRef" @load-data="onTableChange" :ids="tableIds || []"></detail>

    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入应收明细"
      :download-fn="() => exportExcel('应收明细数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import Detail from './components/Detail.vue'
// import { renderDict } from '@/utils/render'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage, exportExcel, importExcel } from './apis'
// Modal,
import { message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
})
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const activeTab = ref('1')
const search = ref({})
const searchList = reactive([])
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const tableIds = computed(() => {
  return list.value.map((item) => item.id)
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id', true)

const defaultColumns = [
  { title: '单据ID', dataIndex: 'number', width: 150, fixed: true },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 150 },
  { title: '合同', dataIndex: 'contractNum', width: 200 },
  { title: '租赁单元', dataIndex: 'leaseUnit' },
  { title: '款项类型', dataIndex: 'paymentType' },
  { title: '结算状态', dataIndex: '' },
  { title: '期数/总期数', dataIndex: '' },
  { title: '款项金额', dataIndex: '' },
  { title: '减免金额', dataIndex: '' },
  { title: '应收金额', dataIndex: 'receiveAmt' },
  { title: '已收金额', dataIndex: 'consumedAmt' },
  { title: '剩余金额', dataIndex: 'notConsumedAmt' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '结清日期', dataIndex: '' },

  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

// 查看核销记录
const turnToPage = () => {}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('楼栋数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>
