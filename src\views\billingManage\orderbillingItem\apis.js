import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/houseDealBill/list',
    params
  })
}
// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/houseDealBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/houseDealBill/importExcel', data, controller)
}
