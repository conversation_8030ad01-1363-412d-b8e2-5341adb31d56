<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <a-tabs class="!mt-[12px]" v-model:active-key="search.bizStatus" @change="onTableChange">
      <a-tab-pane key="" tab="全部"></a-tab-pane>
      <a-tab-pane key="NotConsumed,PartlyConsumed" tab="待核销"></a-tab-pane>
      <a-tab-pane key="Consumed" tab="已核销"></a-tab-pane>
    </a-tabs>

    <div class="flex justify-between !my-[24px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <span class="primary-btn" @click="rowDel(false)">删除</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>

        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>

        <a-form class="!ml-[40px]" autocomplete="off" layout="inline">
          <a-form-item>
            <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
          </a-form-item>
        </a-form>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowView(record)">详情</span>
          <span class="primary-btn">核销</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn" @click="turnToPage(record)">核销记录</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="rowEdit(record)">编辑</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="rowDel(record)">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @loadData="onTableChange"></add-edit>
    <!-- 详情 -->
    <detail ref="detailRef" @load-data="onTableChange" :ids="tableIds || []"></detail>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import Detail from './components/Detail.vue'
import AddEdit from './components/AddEdit.vue'
// import areaList from '@/json/region.json'
import { renderDict, renderDictTag } from '@/utils/render'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage, deleteBatch, exportExcel, importExcel } from './apis'
import { Modal, message } from 'ant-design-vue'
// import { isOrNotDic } from '@/store/modules/dict.js'
// , useDictStore
// const store = useDictStore()
onMounted(() => {
  onTableChange()
})
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
const tableIds = computed(() => {
  return list.value.map((item) => item.id)
})
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const defaultColumns = [
  { title: '单据ID', dataIndex: 'number', width: 150, fixed: true },
  { title: '客户名称', dataIndex: 'customer', width: 150 },
  { title: '收付款公司', dataIndex: 'manageCompany_dictText', width: 200 },
  {
    title: '单据类型',
    dataIndex: 'landNature',
    width: 120,
    customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  },
  { title: '收付金额', dataIndex: 'actualReceiveAmt', minWidth: 80 },
  { title: '手续费', dataIndex: 'serviceCharge', minWidth: 80 },
  { title: '合计金额', dataIndex: 'sumAmt', minWidth: 80 },
  {
    title: '数据状态',
    dataIndex: 'status',
    minWidth: 80,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '核销情况',
    dataIndex: 'bizStatus',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BizStatus')
  },
  {
    title: '来源',
    dataIndex: 'billSource',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_PayExplainBook_BillSource')
  },
  {
    title: '经办人',
    dataIndex: 'operator_dictText',
    minWidth: 80
  },
  { title: '收款日期', dataIndex: 'receiveDate', minWidth: 80 },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id', true)
const search = ref({
  bizStatus: ''
})
const searchList = reactive([])

// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 编辑
const rowEdit = (row) => {
  addEditRef.value.open(row)
}
// 删除
const rowDel = (data) => {
  Modal.confirm({
    title: '提示',
    content: data ? '确认删除当前收付款记录？' : '确认批量删除选中收付款记录？',
    centered: true,
    onOk: async () => {
      await deleteBatch(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
// 查看核销记录
const router = useRouter()
const turnToPage = () => {
  return router.push({ path: '/writeOff/records', query: {} })
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('楼栋数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>
