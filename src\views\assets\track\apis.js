import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
// 全部
export const getAllList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/idleAssetTracking/allList',
    params
  })
}
// 闲置
export const getIdleAssetTrackingList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/idleAssetTracking/list',
    params
  })
}
// 占用
export const getOccupyAssetTrackingList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/occupyAssetTracking/list',
    params
  })
}
// 借用
export const getBorrowAssetTrackingList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/borrowAssetTracking/list',
    params
  })
}
// 自用
export const getSelfAssetTrackingList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/selfAssetTracking/list',
    params
  })
}

// 盘活记录主表ID查询 闲置
export const queryIdleAssetActivateByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/idleAssetTracking/queryIdleAssetActivateByMainId?id=${id}`
  })
}

// 盘活记录主表ID查询 占用
export const queryOccupyAssetActivateByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/occupyAssetTracking/queryOccupyAssetActivateByMainId?id=${id}`
  })
}

// 盘活记录主表ID查询 借用
export const queryBorrowAssetTrackingInfoByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/borrowAssetTracking/queryBorrowAssetTrackingInfoByMainId?id=${id}`
  })
}

// 盘活记录主表ID查询 自用
export const querySelfAssetTrackingInfoByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/selfAssetTracking/querySelfAssetTrackingInfoByMainId?id=${id}`
  })
}

// 闲置 通过id查询
export const getIdleQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/idleAssetTracking/queryById?id=${id}`
  })
}
// 占用 通过id查询
export const getOccupyQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/occupyAssetTracking/queryById?id=${id}`
  })
}
// 借用 通过id查询
export const getBorrowQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/borrowAssetTracking/queryById?id=${id}`
  })
}
// 自用 通过id查询
export const getSelfQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/selfAssetTracking/queryById?id=${id}`
  })
}

// 闲置添加
export const idleAdd = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/idleAssetTracking/add',
    data
  })
}
// 占用添加
export const occupyAdd = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/occupyAssetTracking/add',
    data
  })
}
// 借用添加
export const borrowAdd = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/borrowAssetTracking/add',
    data
  })
}
// 自用添加
export const selfAdd = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/selfAssetTracking/add',
    data
  })
}

// 闲置提交
export const idleSubmit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/idleAssetTracking/submit',
    data
  })
}
// 占用提交
export const occupySubmit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/occupyAssetTracking/submit',
    data
  })
}
// 借用提交
export const borrowSubmit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/borrowAssetTracking/submit',
    data
  })
}
// 自用提交
export const selfSubmit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/selfAssetTracking/submit',
    data
  })
}

// 闲置编辑
export const idleEdit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/idleAssetTracking/edit',
    data
  })
}
// 占用编辑
export const occupyEdit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/occupyAssetTracking/edit',
    data
  })
}
// 借用编辑
export const borrowEdit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/borrowAssetTracking/edit',
    data
  })
}
// 自用编辑
export const selfEdit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/selfAssetTracking/edit',
    data
  })
}

// 通过id删除 闲置
export const idleDelById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/idleAssetTracking/delete?id=${id}`
  })
}
// 通过id删除 占用
export const occupyDelById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/occupyAssetTracking/delete?id=${id}`
  })
}
// 通过id删除 借用
export const borrowDelById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/borrowAssetTracking/delete?id=${id}`
  })
}
// 通过id删除 自用
export const selfDelById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/selfAssetTracking/delete?id=${id}`
  })
}

// 通过id删除 全部
export const allDeleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/assetTracking/deleteBatch?ids=${ids}`
  })
}

// 通过ids批量删除 闲置
export const idleBatchDel = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/idleAssetTracking/deleteBatch?ids=${ids}`
  })
}
// 通过ids批量删除 占用
export const occupyBatchDel = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/occupyAssetTracking/deleteBatch?ids=${ids}`
  })
}
// 通过ids批量删除 借用
export const borrowBatchDel = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/borrowAssetTracking/deleteBatch?ids=${ids}`
  })
}
// 通过ids批量删除 自用
export const selfBatchDel = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/selfAssetTracking/deleteBatch?ids=${ids}`
  })
}

// 通过id查询 闲置
export const idleQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/idleAssetTracking/queryById?id=${id}`
  })
}
// 通过id查询 占用
export const occupyQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/occupyAssetTracking/queryById?id=${id}`
  })
}
// 通过id查询 借用
export const borrowQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/borrowAssetTracking/queryById?id=${id}`
  })
}
// 通过id查询 自用
export const selfQueryById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/selfAssetTracking/queryById?id=${id}`
  })
}

// 撤回 闲置
export const idleBack = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/idleAssetTracking/back',
    data
  })
}
// 撤回 占用
export const occupyBack = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/occupyAssetTracking/back',
    data
  })
}
// 撤回 借用
export const borrowBack = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/borrowAssetTracking/back',
    data
  })
}
// 撤回 自用
export const selfBack = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/selfAssetTracking/back',
    data
  })
}

//  全部 导出
export const allExportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/assetTracking/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

//  闲置 导出
export const idleExportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/idleAssetTracking/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 占用 导出
export const occupyExportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/occupyAssetTracking/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 借用  导出
export const borrowExportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/borrowAssetTracking/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 自用 导出
export const selfExportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/selfAssetTracking/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

//  全部 导入
export const allImportExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/assetTracking/importExcel', data, controller)
}
//  闲置 导入
export const idleImportExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/idleAssetTracking/importExcel', data, controller)
}
// 占用 导入
export const occupyImportExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/occupyAssetTracking/list', data, controller)
}
// 借用 导入
export const borrowImportExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/borrowAssetTracking/importExcel', data, controller)
}
// 自用 导入
export const selfImportExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/selfAssetTracking/importExcel', data, controller)
}

// 盘活记录 编辑
export const assetRevInfoEdit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/selfAssetTracking/back',
    data
  })
}
// 盘活记录 删除
export const assetRevInfoDel = (id) => {
  return request({
    method: 'get',
    url: `?id=${id}`
  })
}
