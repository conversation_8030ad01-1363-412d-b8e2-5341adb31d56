<template>
  <a-radio-group v-model:value="paymentType" button-style="solid">
    <a-radio-button v-for="item in detail.billList" :key="item.paymentType" :value="item.paymentType">
      {{ item.paymentType_dictText }}
    </a-radio-button>
  </a-radio-group>
  <div class="flex bg-[#f5f7fa] p-[16px] rounded-[8px] mt-[16px]">
    <div class="flex flex-col items-center flex-1">
      <span>应收金额</span>
      <small class="text-tertiary my-[8px] text-[12px]">已到应收日期的款项金额+调整金额</small>
      <span>{{ currentContent.receiveTotalAmount }}</span>
    </div>
    <div class="flex flex-col items-center flex-1">
      <span>已结算金额</span>
      <small class="text-tertiary my-[8px] text-[12px]">已交费核销的应收金额</small>
      <span>{{ currentContent.payAmount }}</span>
    </div>
    <div class="flex flex-col items-center flex-1">
      <span>待结算金额</span>
      <small class="text-tertiary my-[8px] text-[12px]">还未交费核销的应收金额</small>
      <span>{{ currentContent.notPayAmount }}</span>
    </div>
    <div class="flex flex-col items-center flex-1">
      <span>已调整金额</span>
      <small class="text-tertiary my-[8px] text-[12px]">正数为增加，负数为减免</small>
      <span>{{ currentContent.adjustAmount }}</span>
    </div>
    <div class="flex flex-col items-center flex-1">
      <span>未到期金额</span>
      <small class="text-tertiary my-[8px] text-[12px]">还未到应收日期的款项金额</small>
      <span>{{ currentContent.unDueAmount }}</span>
    </div>
  </div>
  <div class="flex items-center justify-between my-[16px]">
    <div class="flex items-center">
      <a-button>
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <s-input v-model="keyword" class="ml-[16px]"></s-input>
    </div>
    <columns-set ref="columnsRef" :default-columns="defaultBillColumns"></columns-set>
  </div>
  <a-table
    :data-source="tableData"
    :columns="billColumns"
    :pagination="false"
    :scroll="{ x: 1600, y: '50vh' }"
  ></a-table>
</template>

<script setup>
const { detail } = defineProps({
  detail: { required: true, type: Object }
})

const defaultBillColumns = [
  { title: '单据编码', dataIndex: 'number', width: 140, fixed: 'left' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '结算状态', dataIndex: 'originalRent' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '款项金额', dataIndex: 'paymentAmount', customRender: ({ text }) => (text ? `${text}元` : '') },
  { title: '调整金额', dataIndex: 'adjustAmount', customRender: ({ text }) => (text ? `${text}元` : '') },
  { title: '应收金额', dataIndex: 'actualReceiveAmount', customRender: ({ text }) => (text ? `${text}元` : '') },
  { title: '已收金额', dataIndex: 'paid', customRender: ({ text }) => (text ? `${text}元` : '') },
  { title: '未收金额', dataIndex: 'residual', customRender: ({ text }) => (text ? `${text}元` : '') },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '结算日期', dataIndex: 'collectionCompany_dictText' },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', customRender: ({ text }) => (text ? `${text}元` : '') },
  { title: '已退金额', dataIndex: 'refunded', customRender: ({ text }) => (text ? `${text}元` : '') },
  { title: '已处理尾差', dataIndex: 'offDifference', customRender: ({ text }) => (text ? `${text}元` : '') }
]
const columnsRef = ref()
const billColumns = computed(() => columnsRef.value?.columns)

const paymentType = ref('全部')
const keyword = ref('')

const tableData = computed(() => {
  if (!(detail.billList && detail.billList.length)) return []
  const data = detail.billList.find((i) => i.paymentType === paymentType.value)
  return data ? data.contractDetailBillsList : []
})

const currentContent = computed(() => {
  if (!(detail.billList && detail.billList.length)) return {}
  const data = detail.billList.find((i) => i.paymentType === paymentType.value)
  return data || {}
})
</script>
