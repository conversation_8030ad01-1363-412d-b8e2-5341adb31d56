<template>
  <div>
    <div class="flex items-center justify-between mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.invoiceName"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <edit-rate ref="editRateRef" @refresh="refresh"></edit-rate>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('项目税率.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel } from './apis.js'
import EditRate from './components/EditRate.vue'
import { Modal, message } from 'ant-design-vue'

const props = defineProps({
  manageCompany: { type: String, default: '' }
})

const params = reactive({
  invoiceName: undefined,
  invoiceType: undefined,
  invoiceForm: undefined,
  simpleCollectionType: undefined,
  policyCode: undefined,
  measureUnit: undefined,
  invoiceTime: undefined,
  invoiceRate: undefined,
  remark: undefined,
  createTime: undefined
})

const searchList = [
  { label: '开票税率', name: 'invoiceRate', type: 's-input' },
  {
    label: '开票类型',
    name: 'invoiceType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_InvoiceType'
  },
  {
    label: '发票类型',
    name: 'invoiceForm',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_ReceiveItemPaymentTypeRate_InvoiceForm	'
  },
  {
    label: '简易征收类别',
    name: 'simpleCollectionType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_ReceiveItemPaymentTypeRate_SimpleCollectionType'
  },
  {
    label: '优惠政策编码',
    name: 'policyCode',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_ReceiveItemPaymentTypeRate_PolicyCode'
  },
  {
    label: '计量单位',
    name: 'measureUnit',
    type: 'dict-select',
    code: 'CT_BAS_MeasureUnit'
  },
  {
    label: '开票时点',
    name: 'invoiceTime',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_ReceiveItemPaymentTypeRate_InvoiceTime'
  },
  { label: '发票品名', name: 'invoiceName', type: 's-input' },
  { label: '备注', name: 'remark', type: 's-input' },
  { label: '创建时间', name: 'createTime', type: 'date' }
]

const defaultColumns = [
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120, fixed: 'left' },
  { title: '开票税率', dataIndex: 'invoiceRate', width: 120 },
  { title: '开票类型', dataIndex: 'invoiceType_dictText', width: 120 },
  { title: '发票类型', dataIndex: 'invoiceForm_dictText', width: 160 },
  { title: '简易征收类别', dataIndex: 'simpleCollectionType_dictText', width: 180 },
  { title: '优惠政策编码', dataIndex: 'policyCode_dictText', width: 180 },
  { title: '计量单位', dataIndex: 'measureUnit_dictText', width: 120 },
  { title: '开票时点', dataIndex: 'invoiceTime_dictText', width: 120 },
  { title: '发票品名', dataIndex: 'invoiceName', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 120 },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

watch(
  () => props.manageCompany,
  (value) => {
    params.manageCompany = value
    if (!value) {
      return
    }
    onTableChange()
  },
  {
    immediate: true
  }
)

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRateRef = ref()
const handleAdd = () => {
  editRateRef.value.open(props.manageCompany)
}
const handleEdit = (data) => {
  editRateRef.value.open(props.manageCompany, data.id)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除款项税率信息？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('项目税率数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
