<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="460px"
    wrap-class-name="common-modal common-import-modal"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :label-col="{ style: { width: '120px' } }" :model="ruleForm" :rules="rules">
      <a-form-item label="手机号" name="phone">
        <a-input
          v-model:value="ruleForm.phone"
          placeholder="请输入手机号"
          :maxlength="64"
          style="width: 100%"
        ></a-input>
      </a-form-item>
      <a-form-item label="验证码" name="verifyCode">
        <a-input-search v-model:value="ruleForm.verifyCode" placeholder="请输入验证码" size="large">
          <template #enterButton>
            <a-button @click="getVerifyCode" :disabled="countdown > 0">
              {{ countdown > 0 ? `重新发送（${countdown}s）` : '获取验证码' }}
            </a-button>
          </template>
        </a-input-search>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { changePhone } from '@/apis/common'
const visible = ref(false)
/**
 * 打开弹窗
 * @param extraParams formData所需的额外参数
 * 之所以放在open函数里，而不放在props里，是为了某些情况下，可以减少父组件的代码量，降低组件之间的耦合度
 */
const open = (data = {}) => {
  if (data.phone) {
    ruleForm.value = data
  }
  visible.value = true
}
defineExpose({ open })
const ruleForm = ref({
  phone: '',
  verifyCode: ''
})
const rules = computed(() => ({
  phone: [{ required: true, message: '请输入手机号', trigger: ['blur'] }],
  verifyCode: [{ required: true, message: '请输入验证码', trigger: ['blur'] }]
}))
const modalTitle = computed(() => {
  return ruleForm.value.phone ? '修改手机号' : '绑定手机号'
})

const countdown = ref(0) // 剩余秒数
const timer = ref(null) // 定时器引用
// 获取验证码
const getVerifyCode = () => {
  if (countdown.value > 0) return // 正在倒计时中，禁止重复点击
  // 实际获取验证码的逻辑（如发送HTTP请求）

  // 假设请求成功后开始倒计时
  countdown.value = 59
  timer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--
    } else {
      clearInterval(timer.value)
      timer.value = null
    }
  }, 1000)
}
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    const { result } = await changePhone({})
    message.success(result)
    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  visible.value = false
}
</script>
