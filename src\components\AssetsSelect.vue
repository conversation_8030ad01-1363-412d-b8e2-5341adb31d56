<!-- 选择资产弹窗 -->
<template>
  <a-select
    v-bind="$attrs"
    v-model:value="localValue"
    :placeholder="placeholder"
    :open="false"
    style="width: 100%"
    @click="handleHouseOwnerClick"
    :options="localOptions"
  />

  <a-modal v-model:open="visible" width="60%" title="资产选择" @ok="handleOk">
    <a-form class="!mb-[12px]" autocomplete="off" :model="search" layout="inline">
      <a-form-item label="类别">
        <a-select class="w-[200px]" v-model:value="search.treeId" placeholder="请选择类别"></a-select>
      </a-form-item>
      <a-form-item label="搜索">
        <a-input class="w-[200px]" v-model:value="search.keyword" placeholder="请输入">
          <template #prefix>
            <span class="a-icon-search text-primary"></span>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item>
        <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
      </a-form-item>
    </a-form>

    <a-table
      ref="tableRef"
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ type: 'radio', onChange: radioChange }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>
<script setup>
// import { message } from 'ant-design-vue'
import { getPage } from '../views/assets/manage/apis.js'
import usePageTable from '@/hooks/usePageTable'
import { renderDict, renderDictTag } from '@/utils/render'
import { isOrNotDic } from '@/store/modules/dict.js'
import { projectPage } from '@/views/projects/apis.js'
import areaList from '@/json/region.json'
import { getQueryWyBuildingByMainId, getQueryWyFloorByMainId } from '@/views/assets/manage/apis.js'
onMounted(() => {
  localValue.value = modelValue || undefined
  localOptions.value = options || []
  onTableChange()
})
const emits = defineEmits(['update:modelValue'])
const { modelValue, options } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  options: { default: () => [], type: Array },
  placeholder: { default: '请选择', type: String }
})
const localValue = ref(undefined)
const handleHouseOwnerClick = () => {
  visible.value = true
}
const visible = ref(false)
const search = ref({
  number: '',
  name: '',
  wyProject: undefined,
  wyBuilding: undefined,
  wyFloor: undefined,
  pcaCode: [],
  collectionCompany: '',
  ownerCompany: '',
  manageCompany: '',
  assetsType: '',
  bizStatus: '',
  status: '',
  acquisitionMethod: '',
  propertyRightStatus: '',
  propertyUse: '',
  landNature: '',
  landConstructionSituation: '',
  houseType: '',
  buildStructrue: '',
  houseModel: '',
  firefightingRate: '',
  houseSafeRate: '',
  createTime: '',
  updateTime: '',
  auditTime: '',
  detailAddress: '',
  warrantsDate: '',
  isUnionCertificate: undefined,
  buildYear: ''
})
const searchList = reactive([
  { label: '资产编号', name: 'number', type: 'input', placeholder: '请输入资产编号' },
  { label: '资产名称', name: 'name', type: 'input', placeholder: '请输入资产名称' },
  // { label: '权证号', name: 'name', type: 'input', placeholder: '请输入权证号' },

  {
    label: '关联项目',
    name: 'wyProject',
    type: 'api',
    placeholder: '请选择关联项目',
    listFunc: projectPage
  },
  {
    label: '楼栋',
    name: 'wyBuilding',
    type: 'api',
    placeholder: '请选择楼栋',
    listFunc: () => getQueryWyBuildingByMainId({ id: '1919929817168166913' })
  },
  {
    label: '楼层',
    name: 'wyFloor',
    type: 'api',
    placeholder: '请选择楼层',
    listFunc: () => getQueryWyFloorByMainId({ id: '' })
  },
  { label: '区域', name: 'pcaCode', type: 'cascader', placeholder: '请选择区域', list: areaList },
  { label: '租金归集公司', name: 'collectionCompany', type: 'deptTree', placeholder: '请选择租金归集公司' },
  { label: '资产权属公司', name: 'ownerCompany', type: 'deptTree', placeholder: '请选择资产权属公司' },
  { label: '物业管理公司', name: 'manageCompany', type: 'deptTree', placeholder: '请选择物业管理公司' },
  { label: '资产类型', name: 'assetsType', type: 'dic', placeholder: '请选择资产类型', code: 'CT_BAS_AssetsType' },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dic',
    placeholder: '请选择业务状态',
    code: 'CT_BASE_ENUM_HouseOwner_BizStatus'
  },
  { label: '启用状态', name: 'status', type: 'dic', placeholder: '请选择启用状态', code: 'CT_BASE_ENUM_AuditStatus' },
  {
    label: '取得来源',
    name: 'acquisitionMethod',
    type: 'dic',
    placeholder: '请选择取得来源',
    code: 'CT_BAS_AcquisitionMethod'
  },
  {
    label: '产权情况',
    name: 'propertyRightStatus',
    type: 'dic',
    placeholder: '请选择产权情况',
    code: 'CT_BASE_ENUM_HouseOwner_PropertyRightStatus'
  },
  { label: '产权用途', name: 'propertyUse', type: 'dic', placeholder: '请选择产权用途', code: 'CT_BAS_PropertyUse' },
  { label: '使用权类型', name: 'landNature', type: 'dic', placeholder: '请选择使用权类型', code: 'CT_BAS_LandNature' },
  {
    label: '土地建设情况',
    name: 'landConstructionSituation',
    type: 'dic',
    placeholder: '请选择土地建设情况',
    code: 'CT_BAS_LandCS'
  },
  {
    label: '房产类型',
    name: 'houseType',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BASE_ENUM_HouseOwner_HouseType'
  },
  {
    label: '建筑结构',
    name: 'buildStructrue',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BAS_BuildStructrue'
  },
  { label: '户型', name: 'houseModel', type: 'input', placeholder: '请输入户型' },
  {
    label: '消防等级',
    name: 'firefightingRate',
    type: 'dic',
    placeholder: '请选择消防等级',
    code: 'CT_BAS_FirefightingRate'
  },
  {
    label: '房屋安全等级',
    name: 'houseSafeRate',
    type: 'dic',
    placeholder: '请选择房屋安全等级',
    code: 'CT_BAS_HouseSafeRate'
  },
  { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
  // { label: '创建人', name: 'createBy', type: 'api', placeholder: '请选择创建人' },
  { label: '最近修改时间', name: 'updateTime', type: 'date', placeholder: '请选择最近修改时间' },
  // { label: '最近修改人', name: 'updateBy', type: 'api', placeholder: '请选择最近修改人' },
  { label: '审核时间', name: 'auditTime', type: 'date', placeholder: '请选择审核时间' },
  // { label: '审核人', name: 'auditBy', type: 'api', placeholder: '请选择审核人' },
  { label: '详细地址', name: 'detailAddress', type: 'input', placeholder: '请输入详细地址' },
  { label: '权证获得日期', name: 'warrantsDate', type: 'date', placeholder: '请选择权证获得日期' },
  {
    label: '房地权证合一',
    name: 'isUnionCertificate',
    type: 'select',
    placeholder: '请选择房地权证合一',
    list: isOrNotDic
  },
  { label: '建筑年份', name: 'buildYear', type: 'year', format: 'YYYY', placeholder: '请选择建筑年份' }
])

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
const columns = [
  { title: '资产名称', dataIndex: 'name', width: 150, fixed: true },
  { title: '权证号', dataIndex: 'ownerNumber', width: 150 },
  { title: '产权用途', dataIndex: 'propertyUse', width: 200 },
  {
    title: '使用权类型',
    dataIndex: 'landNature',
    width: 120,
    customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  },
  { title: '地址', dataIndex: 'detailAddress', minWidth: 80 },
  {
    title: '启用状态',
    dataIndex: 'status',
    minWidth: 80,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_BizStatus')
  },
  { title: '建筑面积', dataIndex: 'structureArea', minWidth: 80 },
  { title: '宗地面积', dataIndex: 'floorArea', minWidth: 80 },
  {
    title: '房产类型',
    dataIndex: 'houseType',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_HouseType')
  },
  {
    title: '资产类型',
    dataIndex: 'assetsType',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BAS_AssetsType')
  },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText', minWidth: 80 },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', minWidth: 80 },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', minWidth: 80 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '备注', dataIndex: 'remark', minWidth: 80 },
  { title: '资产编号', dataIndex: 'number', minWidth: 80 }
]

const localOptions = ref([])
const radioChange = (selectedRowKeys, selectedRows) => {
  localValue.value = selectedRowKeys[0]
  localOptions.value = selectedRows.map((item) => {
    return {
      value: item.id,
      label: item.name
    }
  })
}
const handleOk = () => {
  emits('update:modelValue', localValue.value)
  visible.value = false
}
</script>
