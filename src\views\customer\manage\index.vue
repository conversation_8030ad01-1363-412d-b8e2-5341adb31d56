<template>
  <div>
    <h2 class="text-[18px] font-bold mb-[28px]">{{ pageTitle }}</h2>
    <div class="flex justify-between items-center">
      <a-tabs v-model:active-key="activeTabKey" class="flex-1">
        <a-tab-pane key="all" tab="全部"></a-tab-pane>
        <a-tab-pane key="Intention" tab="意向"></a-tab-pane>
        <a-tab-pane key="Official" tab="正式"></a-tab-pane>
      </a-tabs>
    </div>

    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus mr-1"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right mr-1"></i>
          导入
        </a-button>
        <a-button @click="handleExport">
          <i class="a-icon-export-right mr-1"></i>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete" @click="handleBatchDelete">删除</a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-1"></i>
          </a-button>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.name"
          placeholder="搜索名称"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
        <div class="flex items-center ml-[16px]">
          <a-checkbox v-model:checked="viewEnabled">仅看启用</a-checkbox>
        </div>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch
            :checked="record.status === 'ENABLE'"
            :loading="statusLoading"
            @change="handleStatusChange(record)"
          />
        </template>
        <template v-if="column.dataIndex === 'isFollow'">
          <span>{{ record.isFollow ? '是' : '否' }}</span>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" @click="handleEdit(record)">编辑</a-menu-item>
                <a-menu-item key="newContract">新建合同</a-menu-item>
                <a-menu-item key="newFollowRecord">新建跟进记录</a-menu-item>
                <a-menu-item key="viewRelatedContracts">查看关联合同</a-menu-item>
                <a-menu-item key="viewFollowRecords">查看跟进记录</a-menu-item>
                <a-menu-item key="viewReceivableDetails">查看应收明细</a-menu-item>
                <a-menu-item key="viewPaymentRecords">查看收付款记录</a-menu-item>
                <a-menu-item key="delete" @click="handleDelete(record)">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('客户导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>
    <edit-customer ref="editDrawerRef" @refresh="onTableChange" />
    <customer-detail ref="detailDrawerRef" :data-list="list" @refresh="onTableChange" />
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import EditCustomer from './components/EditCustomer.vue'
import CustomerDetail from './components/CustomerDetail.vue'
import { getCustomerList, deleteCustomer, batchDeleteCustomer, updateStatus, exportExcel, importExcel } from './apis'

const route = useRoute()

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getCustomerList)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const activeTabKey = ref('all')
const columnSetRef = ref()
const editDrawerRef = ref()
const detailDrawerRef = ref()
const exportLoading = ref(false)
const statusLoading = ref(false)
const commonImportRef = ref()
const viewEnabled = ref(false)

const searchParams = reactive({
  name: undefined,
  status: undefined,
  customerStatus: undefined,
  customerType: undefined,
  linkmanPhone: undefined,
  customerSource: undefined,
  manageCompany: undefined,
  initRequire: undefined,
  maintainPerson: undefined,
  maintainDate: undefined,
  isFollow: undefined
})

const searchList = [
  {
    label: '客户类型',
    name: 'customerType',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_CustomerType',
    placeholder: '请选择客户类型'
  },
  { label: '联系电话', name: 'linkmanPhone', type: 's-input', placeholder: '请输入联系电话' },
  {
    label: '客户来源',
    name: 'customerSource',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_CustomerSource',
    placeholder: '请选择客户来源'
  },
  { label: '管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择管理公司' },
  {
    label: '客户状态',
    name: 'customerStatus',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_Customer_CustomerStatus',
    placeholder: '请选择客户状态'
  },
  {
    label: '启用状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_BaseStatus',
    placeholder: '请选择启用状态'
  },
  { label: '初步需求', name: 'initRequire', type: 's-input', placeholder: '请输入初步需求' },
  { label: '维护人', name: 'maintainPerson', type: 'user-select', placeholder: '请选择维护人' },
  { label: '维护时间', name: 'maintainDate', type: 'date', placeholder: '请选择维护时间' }
]

const defaultColumns = [
  { title: '客户名称', dataIndex: 'name', width: 200, ellipsis: true, fixed: 'left' },
  { title: '客户类型', dataIndex: 'customerType_dictText', width: 120 },
  { title: '联系电话', dataIndex: 'linkmanPhone', width: 120 },
  { title: '客户来源', dataIndex: 'customerSource_dictText', width: 120 },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '客户状态', dataIndex: 'customerStatus_dictText', width: 120 },
  { title: '启用状态', dataIndex: 'status', width: 120 },
  { title: '初步需求', dataIndex: 'initRequire', width: 160, ellipsis: true },
  { title: '维护人', dataIndex: 'maintainPerson_dictText', width: 120 },
  { title: '维护时间', dataIndex: 'maintainDate', width: 120 },
  { title: '是否跟进', dataIndex: 'isFollow', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)
const pageTitle = computed(() => route.meta.title)

/**
 * 新增客户
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 编辑客户
 * @param {Object} record - 客户行数据
 */
const handleEdit = (record) => {
  editDrawerRef.value.open(record)
}

/**
 * 查看客户详情
 * @param {Object} record - 客户记录
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 打开导入弹窗
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出客户数据
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  try {
    await exportExcel('客户清单.xls', searchParams)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

/**
 * 更新客户状态
 * @param {Object} record - 客户数据
 */
const handleStatusChange = async (record) => {
  if (statusLoading.value) return
  statusLoading.value = true
  try {
    await updateStatus({ ids: record.id, status: record.status === 'DISABLE' ? 'ENABLE' : 'DISABLE' })
    message.success('更新状态成功')
  } finally {
    statusLoading.value = false
  }
  onTableChange()
}

/**
 * 删除单个客户
 * @param {Object} record - 客户记录
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除客户"${record.name || record.number}"吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteCustomer({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 批量删除客户
 */
const handleBatchDelete = () => {
  if (!selectedRowKeys.value || selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个客户吗？此操作不可恢复！`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await batchDeleteCustomer({ ids: selectedRowKeys.value.join(',') })
      message.success('批量删除成功')
      clearSelection()
      onTableChange()
    }
  })
}

/**
 * 表格变化处理
 * @param {Object} params - 分页参数
 */
const onTableChange = ({ pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo, pageSize, ...searchParams })
}

/**
 * 搜索输入处理（防抖）
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

watch(viewEnabled, (val) => {
  searchParams.status = val ? 'ENABLE' : undefined
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
})

watch(activeTabKey, (newVal) => {
  switch (newVal) {
    case 'Intention':
      searchParams.customerStatus = 'Intention'
      break
    case 'Official':
      searchParams.customerStatus = 'Official'
      break
    default:
      searchParams.customerStatus = undefined
  }
  onTableChange()
})

onMounted(() => {
  onTableChange()
})
</script>
