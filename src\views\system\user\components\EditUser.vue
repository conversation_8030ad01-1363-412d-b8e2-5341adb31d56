<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="600px"
    :wrap-class-name="`common-modal${disabled ? ' watch-user-modal' : ''}`"
    :confirm-loading="confirmLoading"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '84px' } }"
      :disabled="disabled"
      autocomplete="off"
    >
      <a-form-item label="用户账号" name="username">
        <a-input
          v-model:value="form.username"
          placeholder="请输入用户账号"
          :maxlength="30"
          :disabled="Boolean(form.id)"
        />
      </a-form-item>
      <a-form-item label="用户姓名" name="realname">
        <a-input v-model:value="form.realname" placeholder="请输入用户姓名" :maxlength="10" />
      </a-form-item>
      <a-form-item label="登录密码" name="password" v-if="!form.id">
        <a-input-password v-model:value="form.password" placeholder="请输入登录密码"></a-input-password>
      </a-form-item>
      <a-form-item label="确认密码" name="confirmPassword" v-if="!form.id">
        <a-input-password v-model:value="form.confirmPassword" placeholder="请输入确认密码"></a-input-password>
      </a-form-item>
      <a-form-item label="工号" name="workNo">
        <a-input v-model:value="form.workNo" placeholder="请输入工号" :maxlength="30" />
      </a-form-item>
      <a-form-item label="角色" name="selectedroles">
        <a-select v-model:value="form.selectedroles" mode="multiple" placeholder="请选择角色">
          <a-select-option v-for="item in roleList" :key="item.id" :value="item.id">
            {{ item.roleName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="所属部门" name="selecteddeparts">
        <dept-tree-select v-model="form.selecteddeparts" multiple></dept-tree-select>
      </a-form-item>
      <a-form-item label="头像" name="avatar">
        <img-upload v-model="form.avatar" :disabled="disabled"></img-upload>
      </a-form-item>
      <a-form-item label="生日" name="birthday">
        <a-date-picker v-model:value="form.birthday" value-format="YYYY-MM-DD" style="width: 100%" />
      </a-form-item>
      <a-form-item label="性别" name="sex">
        <dict-select v-model="form.sex" code="sex"></dict-select>
      </a-form-item>
      <a-form-item label="邮箱" name="email">
        <a-input v-model:value="form.email" placeholder="请输入邮箱" :maxlength="200" />
      </a-form-item>
      <a-form-item label="手机号码" name="phone">
        <a-input v-model:value="form.phone" placeholder="请输入手机号码" :maxlength="11" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addUser, editUser, checkField, queryUserRole, queryUserDepartList } from '../apis'
import { queryAllNoByTenant } from '@/views/system/role/apis'
import { validatePassword, emailRegexp, phoneRegexp } from '@/utils/validate'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const disabled = ref(false)
const open = (data, isDisabled) => {
  visible.value = true
  disabled.value = isDisabled
  loadRoleList()
  if (data) {
    Object.assign(form, {
      id: data.id,
      username: data.username,
      realname: data.realname,
      workNo: data.workNo,
      avatar: data.avatar || '',
      birthday: data.birthday || '',
      sex: data.sex || '',
      email: data.email,
      phone: data.phone
    })
    loadUserDetail(data.id)
  }
}

const modalTitle = computed(() => {
  if (disabled.value) return '查看用户'
  return form.id ? '编辑用户' : '新增用户'
})

const loadUserDetail = async (userId) => {
  const data = await Promise.allSettled([queryUserRole({ userid: userId }), queryUserDepartList({ userId })])
  if (data[0].status === 'fulfilled') {
    form.selectedroles = data[0].value.result
  }
  if (data[1].status === 'fulfilled') {
    form.selecteddeparts = getAllIdByTree(data[1].value.result)
  }
}

// 递归遍历树形数据，得到全部id
const getAllIdByTree = (tree) => {
  const ids = []
  function traverse(nodes) {
    for (const node of nodes) {
      if (node.value !== undefined) {
        ids.push(node.value)
      }
      if (Array.isArray(node.children) && node.children.length > 0) {
        traverse(node.children)
      }
    }
  }
  traverse(tree)
  return ids
}

const form = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  realname: '',
  workNo: '',
  selectedroles: [],
  selecteddeparts: [],
  avatar: '',
  birthday: '',
  sex: '',
  email: '',
  phone: ''
})

const roleList = ref([])
const loadRoleList = async () => {
  const { result } = await queryAllNoByTenant()
  roleList.value = result
}

const validateUsername = async (rule, value) => {
  if (form.id) return Promise.resolve()
  if (!value) return Promise.reject('请输入用户账号')
  try {
    const { success } = await checkField({ tableName: 'sys_user', fieldName: 'username', fieldVal: value })
    return success ? Promise.resolve() : Promise.reject('用户账号已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const validateWorkNo = async (rule, value) => {
  if (!value) return Promise.reject('请输入工号')
  try {
    const { success } = await checkField({
      tableName: 'sys_user',
      fieldName: 'work_no',
      fieldVal: value,
      dataId: form.id || undefined
    })
    return success ? Promise.resolve() : Promise.reject('工号已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const validatePhone = async (rule, value) => {
  if (!value) return Promise.reject('请输入手机号码')
  if (!phoneRegexp.test(value)) return Promise.reject('请输入正确的11位手机号码')
  try {
    const { success } = await checkField({
      tableName: 'sys_user',
      fieldName: 'phone',
      fieldVal: value,
      dataId: form.id || undefined
    })
    return success ? Promise.resolve() : Promise.reject('手机号码已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const validateEmail = async (rule, value) => {
  if (!value) return Promise.reject('请输入邮箱')
  if (!emailRegexp.test(value)) return Promise.reject('请输入正确邮箱')
  try {
    const { success } = await checkField({
      tableName: 'sys_user',
      fieldName: 'email',
      fieldVal: value,
      dataId: form.id || undefined
    })
    return success ? Promise.resolve() : Promise.reject('邮箱已存在')
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const rules = computed(() => ({
  username: [{ required: true, validator: validateUsername, trigger: 'blur' }],
  realname: [{ required: true, message: '请输入用户姓名', trigger: 'blur' }],
  workNo: [{ required: true, validator: validateWorkNo, trigger: 'blur' }],
  password: [{ required: true, validator: validatePassword('登录密码'), trigger: 'blur' }],
  confirmPassword: [{ required: true, validator: validatePassword('确认密码', form.password), trigger: 'blur' }],
  email: [{ required: true, validator: validateEmail, trigger: 'blur' }],
  phone: [{ required: true, validator: validatePhone, trigger: 'blur' }]
}))

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    params.selectedroles = form.selectedroles && form.selectedroles.length ? form.selectedroles.join(',') : ''
    params.selecteddeparts = form.selecteddeparts && form.selecteddeparts.length ? form.selecteddeparts.join(',') : ''
    if (form.id) {
      delete params.password
      delete params.confirmPassword
      await editUser(params)
    } else {
      await addUser(params)
    }
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.username = ''
  form.password = ''
  form.confirmPassword = ''
  form.realname = ''
  form.workNo = ''
  form.selectedroles = []
  form.selecteddeparts = []
  form.avatar = ''
  form.birthday = ''
  form.sex = ''
  form.email = ''
  form.phone = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.watch-user-modal {
  .ant-modal-footer {
    display: none;
  }
}
</style>
