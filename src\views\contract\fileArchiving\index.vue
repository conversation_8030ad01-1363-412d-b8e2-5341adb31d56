<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <s-input
          v-model="params.name"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleRemove(record)">删除</a-menu-item>
                <a-menu-item @click="handleViewContract(record)">查看合同</a-menu-item>
                <a-menu-item @click="handleAudit(record, true)">审核</a-menu-item>
                <a-menu-item @click="handleAudit(record, false)">反审核</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail ref="detailRef" :data-list="list" @refresh="refreshFromDetail"></detail>
    <contract-detail ref="contractDetailRef" :data-list="[]"></contract-detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('资料归档导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag } from '@/utils/render'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'

const route = useRoute()

const pageTitle = computed(() => route.meta.title)

const params = reactive({
  id: undefined,
  number: undefined,
  bizDate: undefined,
  fileFillDepart: undefined,
  fileFillType: undefined,
  status: undefined,
  contract: undefined,
  signDate: undefined,
  customer: undefined,
  contractType: undefined,
  operator: undefined,
  operatorDepart: undefined,
  startDate: undefined,
  expireDate: undefined,
  leaseUnit: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  attachmentIds: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined,
  ctrlUnit: undefined
})

const defaultColumns = [
  { title: '单据ID', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 120 },
  { title: '合同编号', dataIndex: 'bizStatus', width: 100 },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 160 },
  { title: '单据状态', dataIndex: 'remark', width: 100, customRender: ({ text }) => renderDictTag(text, '', 'dot') },
  { title: '归档部门', dataIndex: 'signDate', width: 120 },
  { title: '归档类型', dataIndex: 'operator_dictText', width: 140 },
  { title: '业务日期', dataIndex: 'contractType_dictText', width: 130 },
  { title: '创建时间', dataIndex: 'wyBuildingCount', width: 140 },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

const contractDetailRef = ref()
const handleViewContract = (data) => {
  contractDetailRef.value.open(data.id)
}

const handleAudit = async (data, result) => {
  result ? await audit({ id: data.id }) : await unAudit({ id: data.id })
  message.success('保存成功')
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除该项？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('资料归档数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
