import request from '@/apis/http'

export const getUserList = (params) => {
  return request({
    method: 'get',
    url: '/sys/user/listAll',
    params
  })
}

export const queryUserList = (params) => {
  return request({
    method: 'get',
    url: '/sys/user/queryUserComponentData',
    params
  })
}

export const addUser = (data) => {
  return request({
    method: 'post',
    url: '/sys/user/add',
    data
  })
}

export const editUser = (data) => {
  return request({
    method: 'post',
    url: '/sys/user/edit',
    data
  })
}

export const deleteUser = (params) => {
  return request({
    method: 'delete',
    url: '/sys/user/deleteBatch',
    params
  })
}

export const checkField = (params) => {
  return request({
    method: 'get',
    url: '/sys/duplicate/check',
    params
  })
}

export const queryUserRole = (params) => {
  return request({
    method: 'get',
    url: '/sys/user/queryUserRole',
    params,
    showErrorMsg: false
  })
}

export const queryUserDepartList = (params) => {
  return request({
    method: 'get',
    url: '/sys/user/userDepartList',
    params,
    showErrorMsg: false
  })
}

export const changePassword = (data) => {
  return request({
    method: 'put',
    url: '/sys/user/changePassword',
    data
  })
}

// 冻结用户
export const frozenBatch = (data) => {
  return request({
    method: 'put',
    url: '/sys/user/frozenBatch',
    data
  })
}
