<template>
  <a-modal
    v-model:visible="visible"
    :title="`${formData.id ? '编辑' : '新建'}客户跟进记录`"
    @ok="handleSave"
    @cancel="handleCancel"
    :confirm-loading="confirmLoading"
    width="800px"
    class="common-modal"
  >
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="跟进客户" name="followCustomer">
        <api-select
          v-model="formData.followCustomer"
          :async-fn="() => getCustomerList({ pageNo: 1, pageSize: 10000 })"
          :field-names="{ label: 'name', value: 'id' }"
          placeholder="请选择客户"
        ></api-select>
      </a-form-item>

      <a-form-item label="跟进人" name="followPerson">
        <a-form-item-rest>
          <user-select v-model="formData.followPerson" placeholder="请选择跟进人" />
        </a-form-item-rest>
      </a-form-item>

      <a-form-item label="跟进方式" name="followMethod">
        <dict-select
          v-model="formData.followMethod"
          placeholder="选择跟进方式"
          code="CT_BASE_ENUM_FollowRecord_FollowMethod"
        ></dict-select>
      </a-form-item>

      <a-form-item label="参观租赁单元" name="visitLeaseUnit">
        <lease-unit-selector v-model="formData.visitLeaseUnit" placeholder="请选择租赁单元" />
      </a-form-item>

      <a-form-item label="意向面积" name="intendArea">
        <a-input-number v-model:value="formData.intendArea" :precision="2" style="width: 100%" addon-after="m²" />
      </a-form-item>

      <a-form-item label="登记时间" name="followTime">
        <a-date-picker v-model:value="formData.followTime" style="width: 100%" value-format="YYYY-MM-DD" />
      </a-form-item>

      <a-form-item label="意向入住时间" name="moveIntendTime">
        <a-date-picker v-model:value="formData.moveIntendTime" style="width: 100%" value-format="YYYY-MM-DD" />
      </a-form-item>

      <a-form-item label="布局偏好" name="layoutPreference">
        <a-textarea
          v-model:value="formData.layoutPreference"
          :maxlength="200"
          show-count
          placeholder="请输入布局偏好"
        />
      </a-form-item>

      <a-form-item label="配套设施需求" name="supportFacilityRequire">
        <a-textarea
          v-model:value="formData.supportFacilityRequire"
          :maxlength="200"
          show-count
          placeholder="请输入配套设施需求"
        />
      </a-form-item>

      <a-form-item label="其他内容" name="otherContent">
        <a-textarea v-model:value="formData.otherContent" :maxlength="200" show-count placeholder="请输入其他内容" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { getCustomerList } from '@/views/customer/manage/apis'
import { addFollowRecord, editFollowRecord } from '../apis'
import LeaseUnitSelector from './LeaseUnitSelector.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  followCustomer: undefined,
  followPerson: undefined,
  followMethod: undefined,
  visitLeaseUnit: undefined,
  intendArea: undefined,
  followTime: undefined,
  moveIntendTime: undefined,
  layoutPreference: undefined,
  supportFacilityRequire: undefined,
  otherContent: undefined
}

const formData = reactive({ ...formDataDefault })

const rules = {
  followCustomer: [{ required: true, message: '请选择跟进客户' }],
  followPerson: [{ required: true, message: '请选择跟进人' }],
  followMethod: [{ required: true, message: '请选择跟进方式' }],
  followTime: [{ required: true, message: '请选择登记时间' }]
}

/**
 * 保存跟进记录
 */
const handleSave = async () => {
  if (confirmLoading.value) return

  await formRef.value.validateFields()

  try {
    confirmLoading.value = true
    if (formData.id) {
      await editFollowRecord(formData)
      message.success('客户跟进记录编辑成功')
    } else {
      await addFollowRecord(formData)
      message.success('客户跟进记录添加成功')
    }
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 打开编辑对话框
 */
const open = (record) => {
  visible.value = true
  if (record && record.id) {
    Object.assign(formData, record)
  }
  // 从客户详情添加
  if (record && record.followCustomer) {
    formData.followCustomer = record.followCustomer
  }
}

/**
 * 取消编辑并重置表单
 */
const handleCancel = () => {
  formRef.value?.resetFields()
  Object.assign(formData, formDataDefault)

  visible.value = false
  emits('refresh')
}

defineExpose({
  open
})
</script>
