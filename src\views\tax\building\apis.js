import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/buildingTaxJTBill/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/buildingTaxJTBill/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/buildingTaxJTBill/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/buildingTaxJTBill/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/buildingTaxJTBill/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/buildingTaxJTBill/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/buildingTaxJTBill/edit',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/buildingTaxJTBill/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/taxaccrual/buildingTaxJTBill/unAudit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/taxaccrual/buildingTaxJTBill/deleteBatch',
    params
  })
}

// 房产税计提单-生成计提单
export const generateJT = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/buildingTaxJTBill/getBuildingTaxJT',
    params
  })
}

// 房产税计提单分录主表ID查询
export const queryBuildingUseTaxJTBill = (params) => {
  return request({
    method: 'get',
    url: '/biz/taxaccrual/buildingTaxJTBill/queryBuildingTaxJTBillEntryByMainId',
    params
  })
}
