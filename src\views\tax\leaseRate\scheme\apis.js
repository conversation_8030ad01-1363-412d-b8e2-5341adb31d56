import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/bas/receiveItemPaymentTypeRate/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/bas/receiveItemPaymentTypeRate/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemPaymentTypeRate/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemPaymentTypeRate/importExcel',
    data
  })
}

export const add = (data) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemPaymentTypeRate/add',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/receiveItemPaymentTypeRate/edit',
    data
  })
}
export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/bas/receiveItemPaymentTypeRate/deleteBatch',
    params
  })
}
