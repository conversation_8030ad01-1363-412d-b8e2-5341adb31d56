<template>
  <a-drawer
    v-model:open="visible"
    class="edit-contract-drawer common-drawer"
    :title="form.id ? '编辑合同' : '新建合同'"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <circle-steps
        :current="current"
        :step-list="['基础信息及租赁单元', '合同款项', '账单明细', '附件信息']"
        width="900px"
        class="mx-auto mb-[40px]"
      ></circle-steps>
      <step-1 ref="step1Ref" :form="form" v-show="current === 1"></step-1>
      <step-2 ref="step2Ref" :form="form" v-show="current === 2"></step-2>
      <step-3 ref="step3Ref" :form="form" v-show="current === 3"></step-3>
      <step-4 ref="step4Ref" :form="form" v-show="current === 4"></step-4>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleChangeStep('previous')" v-show="current > 1">上一步</a-button>
      <a-button type="primary" @click="handleChangeStep('next')" v-show="current < 4">下一步</a-button>
      <a-button type="primary" :loading="saveLoading" ghost @click="handleSave">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, submit, detail, save, queryBillList, queryContractLeaseUnits, queryContractLeaseFunds } from '../apis.js'
import { message } from 'ant-design-vue'
import Step1 from './Step1.vue'
import Step2 from './Step2.vue'
import Step3 from './Step3.vue'
import Step4 from './Step4.vue'
import Decimal from 'decimal.js'
import dayjs from 'dayjs'
import userStore from '@/store/modules/user.js'

const emit = defineEmits(['refresh'])

const { departs, userInfo } = userStore()

const step1Ref = ref()
const step2Ref = ref()
const step3Ref = ref()

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  } else {
    form.manageCompany = userInfo.value.orgId || ''
    form.operator = userInfo.value.id || ''
    form.operatorDepart = departs.value.length ? departs.value[0].id : ''
    form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD') // 业务日期默认取当天
  }
}

const current = ref(1)

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    if (!Array.isArray(form[key]) && key !== 'attachmentIds') {
      form[key] = result[key]
    }
  }
  const { result: leaseUnitList } = await queryContractLeaseUnits({ id })
  form.contractLeaseUnitsList = leaseUnitList
  const { result: fundsList } = await queryContractLeaseFunds({ id })
  form.contractLeaseFundsList = fundsList.map((item) => {
    if (item.contractLeaseFundsPriceIncreaseWayList && item.contractLeaseFundsPriceIncreaseWayList.length) {
      item.openRule = true // 是否开启递增规则
    }
    return {
      ...item,
      dateRange: [item.startDate, item.expireDate]
    }
  })
  loading.value = false
}

const form = reactive({
  id: '',
  number: '',
  contractNumber: '',
  status: '',
  bizStatus: '',
  bizDate: '',
  signDate: '',
  customer: '',
  contractType: '',
  manageCompany: '',
  operator: '',
  operatorDepart: '',
  pricedType: '',
  startDate: '',
  expireDate: '',
  terminateDate: '',
  terminateReason: '',
  changeReason: '',
  originalLease: '',
  totalArea: '',
  totalRental: '',
  totalRemission: '',
  remark: '',
  attachmentIds: '',
  contractLeaseUnitsList: [], // 租赁单元
  contractLeaseFundsList: [], // 合同款项
  contractDetailBillsVOList: [] // 第三步，账单明细预览，只查看，无需提交
})

/**
 * 上一步/下一步
 * @param {string} type previous | next
 */
const handleChangeStep = async (type) => {
  if (type === 'previous') {
    current.value--
  } else {
    if (current.value === 1) {
      await step1Ref.value.validate()
    } else if (current.value === 2) {
      if (!step2Ref.value.validate()) return
      // 计算总面积
      form.totalArea = form.contractLeaseUnitsList
        .reduce((total, item) => {
          return total.plus(new Decimal(item.leaseArea || 0))
        }, new Decimal(0))
        .toFixed(2)
      const { result } = await queryBillList(getParams())
      form.contractDetailBillsVOList = result.contractDetailBillsVOList
    }
    current.value++
  }
}

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  try {
    saveLoading.value = true
    form.id ? await edit(getParams()) : await save(getParams())
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const getParams = () => {
  return {
    id: form.id,
    number: form.number,
    contractNumber: form.contractNumber,
    signDate: form.signDate,
    customer: form.customer,
    contractType: form.contractType,
    manageCompany: form.manageCompany,
    operator: form.operator,
    operatorDepart: form.operatorDepart,
    pricedType: form.pricedType,
    startDate: form.startDate,
    expireDate: form.expireDate,
    terminateDate: form.terminateDate,
    terminateReason: form.terminateReason,
    changeReason: form.changeReason,
    originalLease: form.originalLease,
    totalArea: form.totalArea,
    // totalRental: 0,
    // totalRemission: 0,
    remark: form.remark,
    attachmentIds: form.attachmentIds,
    contractLeaseUnitsList: form.contractLeaseUnitsList.map((item) => ({
      id: '',
      leaseUnit: item.id,
      detailAddress: item.detailAddress,
      collectionCompany: item.collectionCompany,
      ownerCompany: item.ownerCompany,
      leaseArea: item.leaseArea,
      areaManager: item.areaManager,
      leaseUse: item.leaseUse,
      propertyUse: item.propertyUse,
      firefightingRate: item.firefightingRate,
      remark: item.remark
    })), // 租赁单元
    contractLeaseFundsList: form.contractLeaseFundsList.map((item) => {
      let list = []
      if (
        item.openRule &&
        item.contractLeaseFundsPriceIncreaseWayList &&
        item.contractLeaseFundsPriceIncreaseWayList.length
      ) {
        list = item.contractLeaseFundsPriceIncreaseWayList.map((i) => ({
          ...i,
          id: i.id.includes('.') ? '' : i.id
        }))
      }
      return {
        ...item,
        id: item.id.includes('.') ? '' : item.id,
        amountPerPeriod: new Decimal(Number(item.period || 0)).times(Number(item.amountPerMonth || 0)).toString(),
        startDate: item.dateRange && item.dateRange.length ? item.dateRange[0] : '',
        expireDate: item.dateRange && item.dateRange.length ? item.dateRange[1] : '',
        contractLeaseFundsPriceIncreaseWayList: list
      }
    }) // 合同款项
  }
}

// 检查所有步骤是否正确填写完成
const validateAllSteps = async () => {
  try {
    await step1Ref.value.validate()
  } catch {
    current.value = 1
    message.warning('请检查基础信息及租赁单元是否正确填写完整')
    return
  }
  if (!step2Ref.value.validate()) {
    current.value = 2
    return
  }
  return true
}
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  const isValid = await validateAllSteps()
  if (!isValid) return
  try {
    confirmLoading.value = true
    await submit(getParams())
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (Array.isArray(form[key])) {
      form[key] = []
    } else {
      form[key] = ''
    }
  }
  step1Ref.value.clearValidate()
  current.value = 1
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-contract-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 40px;
  }
  .ant-form-item {
    width: calc(50% - 20px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
}
</style>
