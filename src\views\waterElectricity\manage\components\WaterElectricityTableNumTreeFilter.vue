<template>
  <a-select
    ref="selectRef"
    v-model:value="selectedTreeNode"
    placeholder="请选择分组"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto', padding: '24px' }"
    :dropdown-match-select-width="false"
    class="!ml-[40px] !w-[280px]"
    allow-clear
    @clear="handleClear"
  >
    <template #dropdownRender>
      <div @mousedown.prevent>
        <a-tree v-model:selected-keys="selectedKeys" :tree-data="treeData" default-expand-all @select="onTreeSelect">
          <template #title="{ name, id }">
            <div class="flex items-center group h-[32px]">
              <span>{{ name }}</span>
              <div class="hidden group-hover:flex">
                <a-tooltip title="添加下级">
                  <span class="mx-1 cursor-pointer text-primary">
                    <i class="a-icon-plus" @click.stop="handleAddTreeNode(id)"></i>
                  </span>
                </a-tooltip>
                <a-tooltip title="编辑">
                  <span class="mx-1 cursor-pointer text-primary">
                    <i class="a-icon-edit" @click.stop="handleEditTreeNode(id)"></i>
                  </span>
                </a-tooltip>
                <a-tooltip title="删除">
                  <span class="mx-1 cursor-pointer text-[#ff4d4f]">
                    <i class="a-icon-remove" @click.stop="handleDeleteTreeNode(id)"></i>
                  </span>
                </a-tooltip>
              </div>
            </div>
          </template>
        </a-tree>
        <div class="flex justify-center border-t border-solid border-[#f0f0f0] pt-[16px]">
          <span class="primary-btn" @click="handleAddRootTreeNode">添加根节点</span>
        </div>
      </div>
    </template>
    <a-select-option v-if="selectedName" :value="selectedTreeNode">
      {{ selectedName }}
    </a-select-option>
  </a-select>

  <!-- 编辑弹窗 -->
  <edit-water-electricity-table-num-tree-modal
    ref="editWaterElectricityTableNumTreeModalRef"
    @refresh="handleEditSuccess"
  />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import {
  getWaterElectricityTableNumTree,
  deleteWaterElectricityTableNumTreeById
} from '../apis/waterElectricityTableNumTree'
import EditWaterElectricityTableNumTreeModal from './EditWaterElectricityTableNumTreeModal.vue'

const emits = defineEmits(['treeNodeChange'])

const selectRef = ref()
const editWaterElectricityTableNumTreeModalRef = ref(null)

const treeData = ref([])
const selectedTreeNode = ref()
const selectedName = ref('')
const selectedKeys = ref([])

/**
 * 获取水电表编号树结构数据
 */
const fetchTreeData = async () => {
  const { result } = await getWaterElectricityTableNumTree()
  treeData.value = result || []
}

/**
 * 处理树节点选择
 */
const onTreeSelect = (selectedKeys, { node }) => {
  if (selectedKeys.length) {
    selectedTreeNode.value = node.id
    selectedName.value = node.name
    if (selectRef.value) {
      selectRef.value.blur()
    }
    emits('treeNodeChange', node.id)
  }
}

/**
 * 处理清空选择
 */
const handleClear = () => {
  selectedTreeNode.value = undefined
  selectedName.value = ''
  selectedKeys.value = []
  emits('treeNodeChange', undefined)
}

/**
 * 处理编辑成功后刷新
 */
const handleEditSuccess = () => {
  fetchTreeData()
}

/**
 * 显示编辑弹窗
 */
const showEditModal = (editData = null, parentId = '') => {
  editWaterElectricityTableNumTreeModalRef.value?.open(editData, parentId)
}

/**
 * 处理添加根节点
 */
const handleAddRootTreeNode = () => {
  showEditModal()
}

/**
 * 处理添加子节点
 */
const handleAddTreeNode = (parentId) => {
  showEditModal(null, parentId)
}

/**
 * 处理编辑节点
 */
const handleEditTreeNode = (id) => {
  const editNode = findNodeById(treeData.value, id)
  if (editNode) {
    showEditModal(editNode)
  }
}

/**
 * 根据ID查找树节点
 */
const findNodeById = (nodes, id) => {
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children) {
      const found = findNodeById(node.children, id)
      if (found) return found
    }
  }
  return null
}

/**
 * 处理删除节点
 */
const handleDeleteTreeNode = (id) => {
  Modal.confirm({
    title: '提示',
    content: '确定要删除此节点吗？删除后无法恢复！',
    async onOk() {
      await deleteWaterElectricityTableNumTreeById({ id })
      message.success('删除成功')
      fetchTreeData()
    }
  })
}

watch(selectedTreeNode, (newVal) => {
  if (!newVal) {
    selectedName.value = ''
    selectedKeys.value = []
  }
})

onMounted(() => {
  fetchTreeData()
})
</script>

<style scoped lang="less">
.group:hover {
  .hidden {
    display: flex;
  }
}
</style>
