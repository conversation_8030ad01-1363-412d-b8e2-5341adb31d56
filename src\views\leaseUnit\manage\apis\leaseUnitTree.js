import request from '@/apis/http'

// 分页
export const leaseUnitTreeList = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnitTree/list',
    params
  })
}

// 通过 id 查询
export const getLeaseUnitTreeById = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnitTree/queryById',
    params
  })
}

// 树形结构查询
export const getLeaseUnitTree = (params) => {
  return request({
    method: 'get',
    url: '/bas/leaseUnitTree/queryTreeList',
    params
  })
}

// 添加
export const addLeaseUnitTree = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnitTree/add',
    data
  })
}

// 编辑
export const editLeaseUnitTree = (data) => {
  return request({
    method: 'post',
    url: '/bas/leaseUnitTree/edit',
    data
  })
}

// 通过 id 删除
export const deleteLeaseUnitTree = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseUnitTree/delete',
    params
  })
}

// 批量删除
export const batchDeleteLeaseUnitTree = (params) => {
  return request({
    method: 'delete',
    url: '/bas/leaseUnitTree/deleteBatch',
    params
  })
}
