<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="460px"
    wrap-class-name="common-modal common-import-modal"
    :confirm-loading="confirmLoading"
    :ok-button-props="{ disabled: !fileData.name }"
    :mask-closable="false"
    ok-text="导入"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center justify-between">
      <span>导入时，如果存在同编码数据，则</span>
      <a-radio-group v-model:value="dealType" name="radioGroup">
        <a-radio value="skip">跳过</a-radio>
        <a-radio value="update">更新</a-radio>
      </a-radio-group>
    </div>
    <div class="flex items-center justify-between my-[12px]">
      <span>请先下载excel模板，按模板填写后上传</span>
      <span class="primary-btn" @click="downloadTemplate">
        <i class="a-icon-loading" v-if="downloading"></i>
        <i class="a-icon-download" v-else></i>
        下载导入模板
      </span>
    </div>
    <div class="import-box">
      <img src="@/assets/svgs/upload.svg" width="32" height="32" class="mb-[8px]" />
      <span class="text-tertiary">点击或拖拽文件到这里上传</span>
      <div
        class="absolute z-[10] left-[0] top-[0] w-full h-full px-[16px] pt-[20px] bg-white rounded-[8px]"
        v-if="fileData.name"
      >
        <div class="flex items-center mb-[8px]">
          <img src="@/assets/svgs/excel.svg" width="36" height="36" class="mr-[8px]" />
          <div>
            <div class="line-clamp-1 mb-[4px]">{{ fileData.name }}</div>
            <small class="text-[12px] text-tertiary">{{ fileData.size }}</small>
          </div>
        </div>
        <div class="text-error cursor-pointer" @click="resetFileData" v-if="fileData.error">重新上传</div>
        <div class="flex items-center justify-between" v-if="fileData.progress">
          <div class="w-[294px] h-[8px] rounded-[4px] bg-[#eaf0fe]">
            <div :style="{ width: fileData.progress || '0' }" class="h-full bg-primary rounded-[4px]"></div>
          </div>
          <span class="text-success">
            {{ fileData.progress === '100%' ? '上传完成' : `上传中${fileData.progress}` }}
          </span>
        </div>
      </div>
      <input
        type="file"
        class="w-full h-full absolute left-[0] top-[0] cursor-pointer opacity-0"
        accept=".xlsx, .xls"
        @change="onchange"
      />
    </div>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'

const { downloadFn, uploadFn } = defineProps({
  modalTitle: { type: String, default: '批量导入数据' },
  downloadFn: {
    required: true,
    type: Function,
    validator: (val) => {
      if (typeof val === 'function') return true
      throw new Error('请确认你传入的是一个函数，而不是一个Promise对象')
    }
  },
  uploadFn: {
    required: true,
    type: Function,
    validator: (val) => {
      if (typeof val === 'function') return true
      throw new Error('请确认你传入的是一个函数，而不是一个Promise对象')
    }
  }
})

const emit = defineEmits(['refresh'])

const visible = ref(false)
let extraParams = null // formData所需的额外参数

// 校验open函数传入的data是否是有属性的对象，不能是空对象或者其他任意类型的数据
const isObject = (data) => {
  return typeof data === 'object' && data !== null && !Array.isArray(data) && Object.keys(data).length > 0
}

/**
 * 打开弹窗
 * @param extraParams formData所需的额外参数
 * 之所以放在open函数里，而不放在props里，是为了某些情况下，可以减少父组件的代码量，降低组件之间的耦合度
 */
const open = (data) => {
  if (data && isObject(data)) {
    extraParams = data
  }
  visible.value = true
}

const dealType = ref('skip') // skip跳过 | update更新

const isExcel = (fileType) => {
  return ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(
    fileType
  )
}

const fileData = reactive({
  name: '',
  size: '',
  progress: '',
  formData: null,
  error: false
})

const onchange = (e) => {
  const files = e.target.files
  if (!(files && files.length)) return
  const file = files[0]
  if (!isExcel(file.type)) {
    message.warning('仅限上传.xls .xlsx文件')
    return
  }
  const formData = new FormData()
  formData.append('file', file)
  formData.append('dealType', dealType.value)
  if (extraParams) {
    for (const key in extraParams) {
      formData.append(key, extraParams[key])
    }
  }
  fileData.name = file.name
  fileData.size = getFileSize(file.size)
  fileData.progress = ''
  fileData.formData = formData
  fileData.error = false
  e.target.value = '' // 清空已选文件
}

const resetFileData = () => {
  fileData.name = ''
  fileData.size = ''
  fileData.progress = ''
  fileData.formData = null
  fileData.error = false
}

const getFileSize = (bytes) => {
  const KB = 1024
  const MB = 1024 * 1024
  if (bytes < KB) return `${bytes}b`
  if (bytes >= MB) {
    const sizeInMB = bytes / MB
    return Number.isInteger(sizeInMB) ? `${sizeInMB}mb` : `${sizeInMB.toFixed(2)}mb`
  }
  const sizeInKB = bytes / KB
  return Number.isInteger(sizeInKB) ? `${sizeInKB}kb` : `${sizeInKB.toFixed(2)}kb`
}

const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  try {
    confirmLoading.value = true
    const { result } = await uploadFn(fileData)
    message.success(result)
    confirmLoading.value = false
    handleCancel()
    emit('refresh')
  } catch {
    confirmLoading.value = false
    fileData.progress = ''
    fileData.error = true
  }
}
const handleCancel = () => {
  resetFileData()
  dealType.value = 'skip'
  visible.value = false
}

const downloading = ref(false)
const downloadTemplate = async () => {
  if (downloading.value) return
  try {
    downloading.value = true
    await downloadFn()
    message.success('下载成功')
  } finally {
    downloading.value = false
  }
}

defineExpose({ open })
</script>

<style lang="less" scoped>
.common-import-modal {
  .ant-radio-wrapper {
    &:last-child {
      margin-right: 0;
    }
    span.ant-radio + * {
      padding-inline-end: 0;
    }
  }
  .import-box {
    height: 106px;
    border-radius: 8px;
    background-color: #f7f8fa;
    border: 1px solid #e6e9f0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
  }
}
</style>
