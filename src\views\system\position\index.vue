<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex justify-between my-[24px]">
      <a-form layout="inline" ref="formRef" class="mb-[10px]" autocomplete="off">
        <a-form-item label="职务名称">
          <s-input
            v-model="params.name"
            placeholder="搜索职务名称"
            @keyup.enter="onTableChange({ current: 1, pageSize: pagination.pageSize })"
          ></s-input>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="onTableChange({ current: 1, pageSize: pagination.pageSize })">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
      <a-button type="primary" @click="handleAdd">新增</a-button>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-popconfirm title="是否确认删除？" ok-text="确认" cancel-text="取消" @confirm="handleRemove(record)">
            <span class="primary-btn">删除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <edit-position ref="editPositionRef" @refresh="refreshData"></edit-position>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message } from 'ant-design-vue'
import { page, remove } from './apis'
import EditPosition from './components/EditPosition.vue'

const route = useRoute()
const pageTitle = computed(() => route.meta.title)

const params = reactive({
  column: 'createTime',
  order: 'desc',
  name: ''
})

const columns = [
  { title: '序号', dataIndex: 'index', width: 60 },
  { title: '职务名称', dataIndex: 'name' },
  { title: '操作', dataIndex: 'action', width: 200 }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}
const handleReset = () => {
  params.name = ''
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
}

const editPositionRef = ref()
const handleAdd = () => {
  editPositionRef.value.open()
}
const handleEdit = (row) => {
  editPositionRef.value.open({
    id: row.id,
    name: row.name
  })
}

const refreshData = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const handleRemove = async (data) => {
  await remove({ id: data.id })
  message.success('删除成功')
  let pageNo = pagination.value.current
  if (pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

onMounted(() => {
  onTableChange()
})
</script>
