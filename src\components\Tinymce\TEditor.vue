<template>
  <div class="t-editor">
    <tinymce-editor :id="id" :disabled="disabled" v-model="value" :init="init"></tinymce-editor>
  </div>
</template>

<script setup>
import tinymce from 'tinymce'
import TinymceEditor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver' // 引入主题
import 'tinymce/plugins/image' // 图片上传
import 'tinymce/plugins/lists' // advlist插件扩展默认的list插件(要使用advlist必须也引入lists)
import 'tinymce/plugins/advlist' // 高级列表(即ul和ol)
import 'tinymce/plugins/code' // 编辑源码
import 'tinymce/plugins/fullscreen' // 全屏
import 'tinymce/plugins/paste' // 粘贴
import 'tinymce/plugins/preview' // 预览
import 'tinymce/plugins/wordcount' // 字数统计
import 'tinymce/plugins/nonbreaking' // 插入不间断的空格(解决按tab键后，光标会退出编辑器的问题)
import 'tinymce/plugins/autolink' // 自动链接，当用户输入完整有效的url时，会自动创建超链接
import 'tinymce/plugins/link' // 为选中内容添加超链接
import 'tinymce/plugins/table' // 表格
import './plugins/lineheight' // 行高
import './plugins/letterspacing' // 字间距
import './plugins/indent2em' // 首行缩进
import './plugins/ax_wordlimit' // 字数限制
import './plugins/media' // 视频上传
import { message as antMessage } from 'ant-design-vue'
import { uploadFile as putFile, getAttachmentByIds, getFileAccessHttpUrl } from '@/apis/common'

const props = defineProps({
  id: { type: String, default: 'tinymce' }, // 如果同一个页面有多个富文本剪辑器，则需要传id
  modelValue: { type: String, required: true },
  disabled: { type: Boolean, default: false },
  height: { type: Number, default: 500 },
  maximum: { type: Number, default: 100000 } // 最大字数限制
})

const emit = defineEmits(['update:modelValue'])

const value = ref('')
watch(
  () => props.modelValue,
  (val) => {
    value.value = val
  }
)

watch(value, (val) => {
  emit('update:modelValue', val)
})

const init = {
  language_url: `${import.meta.env.BASE_URL}tinymce/zh_CN.js`, // 语言包的路径
  language: 'zh_CN', // 语言
  skin_url: `${import.meta.env.BASE_URL}tinymce/skins/ui/oxide`, // skin路径
  content_css: `${import.meta.env.BASE_URL}tinymce/skins/ui/oxide/content.min.css`,
  // theme_url: `${import.meta.env.BASE_URL}tinymce/silver/theme.min.js`,
  invalid_elements: 'section,aside,article', // 将这些标签设为无效元素，因为在这些标签里回车换行的话，预览或者发布后，换行都是不生效的
  content_style:
    'p { margin: 0; } p, h1, h2, h3, h4, h5, h6, td, th, div, ul, ol, li, table { line-height: 1.6; } img { max-width: 100%; }', // 直接为编辑区编写css
  height: props.height, // 编辑器高度
  paste_data_images: true, // 粘贴data格式的图像(支持ctrl+v粘贴图片)
  nonbreaking_force_tab: true, // 插入不间断空格的额外配置(解决按tab键后，光标会退出编辑器的问题)
  fontsize_formats: '12px 14px 16px 18px 20px 22px 24px 28px 32px 36px', // 字体大小选择
  convert_urls: false, // 配置不自动转换url(如果不配置的话，在域名相同的情况下，传入的url会自动将域名转为../)
  plugins:
    'image lists advlist code fullscreen paste preview wordcount nonbreaking link autolink table lineheight letterspacing ax_wordlimit media indent2em',
  toolbar: [
    'fullscreen image media undo link table | formatselect | fontsizeselect | bold italic underline strikethrough',
    'forecolor lineheight letterspacing | indent2em alignleft aligncenter alignright bullist | code preview wordcount'
  ],
  branding: false, // 是否禁用“Powered by TinyMCE”
  menubar: false, // 顶部菜单栏显示
  ax_wordlimit_num: props.maximum, // 限制字数
  ax_wordlimit_callback: workLimitError, // 字数超出限制时的回调函数
  ax_wordlimit_delay: 3000, // 让超出字数限制时，执行回调函数的频率不那么快，3s执行一次
  images_upload_handler: (blobInfo, success, failure, progress) => {
    // 上传图片处理函数
    uploadImg(blobInfo, success, failure, progress) // 自定义图片上传处理函数
  },
  file_picker_callback: (callback, value, meta) => {
    // 自定义文件上传处理函数
    uploadFile(callback, value, meta)
  }
}

function workLimitError() {
  antMessage.error(`您已超出字数限制${props.maximum}字`)
}

/**
 * 自定义图片上传处理函数
 * blobInfo: 一个对象，对象里包含上传的文件的信息，blobInfo.blob()方法，得到的是File对象
 * success: 处理函数，如果图片上传成功，则向success方法里传入图片地址
 * failure: 处理函数，如果图片上传失败，则向failure方法里传入错误信息
 * progress: 上传进度条，progress(0)开始上传，progress(100)上传完成
 */
const uploadImg = async (blobInfo, success, failure, progress) => {
  if (blobInfo.blob().size >= 0) {
    progress(0)
    const formData = new FormData()
    formData.append('file', blobInfo.blob())
    formData.append('isAttachment', true)
    const { message } = await putFile(formData)
    const { result } = await getAttachmentByIds(message)
    success(getFileAccessHttpUrl(result[0].filePath))
    progress(100)
  } else {
    failure('图片上传失败')
  }
}

// 自定义文件上传函数
const uploadFile = (callback, value, meta) => {
  const input = document.createElement('input')
  input.setAttribute('type', 'file')
  if (meta.filetype === 'image') {
    input.setAttribute('accept', 'image/png, image/jpg, image/jpeg, image/webp, image/bmp, image/gif')
  } else if (meta.filetype === 'media') {
    input.setAttribute(
      'accept',
      'video/x-ms-wmv, video/mpeg4, video/avi, video/wma, video/mp4, video/quicktime, video/x-flv, video/mpg, video/x-matroska'
    )
  }
  document.body.appendChild(input)
  input.onchange = async () => {
    if (!(input.files && input.files.length)) return
    const file = input.files[0]
    if (meta.filetype === 'image' && !isImage(file.type)) {
      antMessage.warning('您上传的图片格式不正确，请重新上传！')
      return
    }
    if (meta.filetype === 'media' && !isMedia(file.type)) {
      antMessage.warning('您上传的视频文件格式不正确，请重新上传！')
      return
    }
    const formData = new FormData()
    formData.append('file', file)
    formData.append('isAttachment', true)
    const { message } = await putFile(formData)
    const { result } = await getAttachmentByIds(message)
    callback(getFileAccessHttpUrl(result[0].filePath), { title: result[0].fileName })
    document.body.removeChild(input)
  }
  input.click()
}
// 是否是图片
const isImage = (type) => {
  return ['image/png', 'image/jpg', 'image/jpeg', 'image/bmp', 'image/webp', 'image/gif'].includes(type)
}
// 是否是媒体文件(视频文件这类)
const isMedia = (type) => {
  return [
    'video/x-ms-wmv',
    'video/mpeg4',
    'video/avi',
    'video/wma',
    'video/mp4',
    'video/quicktime',
    'video/x-flv',
    'video/mpg',
    'video/x-matroska'
  ].includes(type)
}

onMounted(() => {
  tinymce.init({})
})
</script>
