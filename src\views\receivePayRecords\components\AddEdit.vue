<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    :title="title"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
  >
    <a-form
      :model="ruleForm"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '120px' } }"
      autocomplete="off"
    >
      <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
      <a-row :gutter="24">
        <a-col :span="12">
          <a-form-item label="实际金额" name="actualReceiveAmt">
            <a-input-number
              class="!w-[100%]"
              v-model:value="ruleForm.actualReceiveAmt"
              placeholder="请输入实际金额"
              suffix="元"
              :min="0"
              :precision="2"
            ></a-input-number>
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="手续费" name="serviceCharge">
            <a-input-number
              class="!w-[100%]"
              v-model:value="ruleForm.serviceCharge"
              placeholder="请输入手续费"
              suffix="元"
              :min="0"
              :precision="2"
            ></a-input-number>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="收款公司" name="manageCompany">
            <dept-tree-select v-model="ruleForm.manageCompany" placeholder="请选择管理公司"></dept-tree-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="发生时间" name="receiveDate">
            <a-date-picker
              class="w-[100%]"
              v-model:value="ruleForm.receiveDate"
              picker="date"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              placeholder="请选择发生时间"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <a-form-item label="所属客户" name="customer">
            <dept-tree-select v-model="ruleForm.customer" placeholder="请选择所属客户"></dept-tree-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="实际来款人" name="actualPayor">
            <dept-tree-select v-model="ruleForm.actualPayor" placeholder="请选择实际来款人"></dept-tree-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="经办人" name="operator">
            <user-select
              v-model="ruleForm.operator"
              v-model:display-value="ruleForm.operator_dictText"
              placeholder="请选择经办人"
              title="选择用户"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="业务部门" name="operatorDepart">
            <dept-tree-select v-model="ruleForm.operatorDepart" placeholder="请选择业务部门"></dept-tree-select>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="ruleForm.remark" placeholder="请输入备注" :maxlength="500" show-count />
          </a-form-item>
        </a-col>
      </a-row>

      <div class="flex justify-between items-center mb-[12px] mt-[16px]">
        <h2 class="text-[16px] font-bold">对应核销明细</h2>
        <a-button type="primary" ghost @click="addDetail">添加明细</a-button>
      </div>

      <a-table
        :data-source="ruleForm.payExplainBookEntryList"
        :columns="columns"
        :loading="false"
        :scroll="{ y: 200, x: 1000 }"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'aaaa'">
            <a-input-number
              class="!w-[100%]"
              v-model:value="record.remark"
              placeholder="请输入本次核销金额"
            ></a-input-number>
          </template>
          <template v-if="column.dataIndex === 'remark'">
            <a-input class="!w-[100%]" v-model:value="record.remark" placeholder="请输入备注"></a-input>
          </template>
        </template>
      </a-table>

      <h2 class="text-[16px] font-bold mb-[12px] mt-[40px]">附件</h2>
      <files-upload v-model="ruleForm.attachmentIds" :biz-id="ruleForm.id"></files-upload>
    </a-form>
    <div class="bg-[#f7f8fa] border border-[#E6E9F0] p-[16px] rounded-[8px] mt-[40px]">
      <div
        class="text-[16px] font-bold mb-[8px]"
        :class="curTotalCollection - curTotalWriteOff < 0 ? 'text-error' : 'text-[#1D335C] '"
      >
        {{ showText }}
      </div>
      <div class="text-[14px] text-secondary">
        [本次合计收款] {{ curTotalCollection }} - [本次累计核销] {{ curTotalWriteOff }} =
        {{ resultCount }}
      </div>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleConfirm">提交</a-button>
      <a-button type="primary" ghost @click="handleStash">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
    <receivable-detail-dialog ref="receivableDetailRef"></receivable-detail-dialog>
  </a-drawer>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { submit, add, edit } from '../apis'
import ReceivableDetailDialog from './ReceivableDetail.vue'
const emits = defineEmits(['loadData'])
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data) => {
  if (data) {
    Object.assign(ruleForm, data)
  }
  visible.value = true
  // loadMenuList()
}
defineExpose({ open })
const title = computed(() => {
  return ruleForm.id ? '编辑收付款' : '新增收付款'
})
const ruleForm = reactive({
  id: '',
  manageCompany: '',
  number: '',
  customer: '',
  billSource: '',
  actualPayor: '',
  receiveDate: '',
  actualReceiveAmt: undefined,
  serviceCharge: undefined,
  sumAmt: undefined,
  operator: '',
  operatorDepart: '',
  status: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  auditBy: '',
  auditTime: '',
  attachmentIds: '',
  sourceBillId: '',
  sourceBillEntryId: '',
  ctrlUnit: '',
  payExplainBookEntryList: [
    // {
    //   id: '',
    //   seq: undefined,
    //   paymentType: '',
    //   certificateNo: '',
    //   contractNum: '',
    //   serviceCenter: '',
    //   carportNum: '',
    //   park: '',
    //   waterEleTableNum: '',
    //   subWaterEleTableNum: '',
    //   leaseUnit: '',
    //   isDeposit: true,
    //   receiveBeginDate: '',
    //   receiveEndDate: '',
    //   incomeBelongYm: '',
    //   receiveAmt: undefined,
    //   consumedAmt: undefined,
    //   notConsumedAmt: undefined,
    //   collectionCompany: '',
    //   invoiceNum: '',
    //   invoiceType: '',
    //   isInvoice: true,
    //   operator: '',
    //   operatorDepart: '',
    //   turnInvoiceReqBill: true,
    //   turnReturnReqBill: true,
    //   pressPayReturn: true,
    //   parent: '',
    //   remark: '',
    //   sourceBillId: '',
    //   sourceBillEntryId: ''
    // }
  ]
})
const rules = computed(() => {
  return {
    actualReceiveAmt: [{ required: true, message: '请输入实际金额', trigger: ['blur'] }],
    serviceCharge: [{ required: true, message: '请输入手续费', trigger: ['blur'] }],
    manageCompany: [{ required: true, message: '请选择收款公司', trigger: ['change'] }],
    receiveDate: [{ required: true, message: '请选择发生日期', trigger: ['change'] }],
    customer: [{ required: true, message: '请选择所属客户', trigger: ['change'] }],
    actualPayor: [{ required: true, message: '请选择实际来款人', trigger: ['change'] }],
    operator: [{ required: true, message: '请选择经办人', trigger: ['change'] }],
    operatorDepart: [{ required: true, message: '请选择业务部门', trigger: ['change'] }]
  }
})

const columns = [
  { title: '账单ID', dataIndex: 'number', width: 180, fixed: true },
  { title: '款项类型', dataIndex: 'paymentType' },
  { title: '租赁单元', dataIndex: 'leaseUnit' },
  { title: '款项金额', dataIndex: 'receiveAmt' },
  { title: '已核销', dataIndex: 'consumedAmt' },
  { title: '待核销', dataIndex: 'notConsumedAmt' },
  { title: '应收日期', dataIndex: 'receiveBeginDate' },
  { title: '本次核销金额', dataIndex: 'aaaa', width: 200 },
  { title: '备注', dataIndex: 'remark', width: 200 }
]

const curTotalCollection = ref(10000.0)
const curTotalWriteOff = ref(5030.0)
const resultCount = computed(() => {
  return curTotalCollection.value - curTotalWriteOff.value
})
const showText = computed(() => {
  if (resultCount.value > 0) {
    return `剩余待核销：${resultCount.value}`
  }
  if (resultCount.value === 0) {
    return '已全部核销'
  }
  return `累计核销超出实际金额，请下调：${resultCount.value}`
})

// 添加明细
const receivableDetailRef = ref()
const addDetail = () => {
  receivableDetailRef.value.open()
}

// 提交
const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  const { result } = await submit(ruleForm)
  message.success(result)
}

// 暂存
const handleStash = async () => {
  await formRef.value.validate()
  const { result } = await (ruleForm.id ? edit(ruleForm) : add(ruleForm))
  message.success(result)
  emits('loadData')
}
// 取消
const handleCancel = () => {
  formRef.value.clearValidate()
  visible.value = false
}
</script>
