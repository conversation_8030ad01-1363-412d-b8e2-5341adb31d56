<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" width="1072px" :mask-closable="false">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 || dataList.length === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="delete" @click="handleDelete">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">{{ detailData.name }}</h2>
        <status-tag :dict-value="detailData.status" dict-code="CT_BASE_ENUM_BaseStatus"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        编号：{{ detailData.number || '-' }} | {{ detailData.createBy_dictText }} 创建于 {{ detailData.createTime }}
      </div>

      <div class="mb-[40px]">
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">编码(表号): {{ detailData.number || '-' }}</span>
          <span class="w-[50%]">名称: {{ detailData.name || '-' }}</span>
          <span class="w-[50%]">倍率: {{ detailData.doubleRate || '-' }}</span>
          <span class="w-[50%]">类型: {{ detailData.type_dictText || '-' }}</span>
          <span class="w-[50%]">属性: {{ detailData.property_dictText || '-' }}</span>
          <span class="w-[50%]">分组: {{ detailData.treeId_dictText || '-' }}</span>
          <span class="w-[50%]">地址: {{ detailData.address || '-' }}</span>
          <span class="w-[50%]">资产权属公司: {{ detailData.ownerCompany_dictText || '-' }}</span>
          <span class="w-[50%]">租金归集公司: {{ detailData.collectionCompany_dictText || '-' }}</span>
          <span class="w-[50%]">物业管理公司: {{ detailData.manageCompany_dictText || '-' }}</span>
          <span class="w-[100%]">单价: {{ detailData.price || '-' }}</span>
          <span class="w-[100%]">备注: {{ detailData.remark || '-' }}</span>
        </div>
      </div>
    </a-spin>
  </a-drawer>
  <edit-water-electricity ref="editDrawerRef" @refresh="refreshData" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { queryWaterElectricityById, deleteWaterElectricity } from '../apis/waterElectricity'
import EditWaterElectricity from './EditWaterElectricity.vue'

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const emits = defineEmits(['refresh', 'edit'])

const visible = ref(false)
const loading = ref(false)
const editDrawerRef = ref()
const detailData = ref({})

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

/**
 * 打开详情抽屉
 */
const open = async (record) => {
  visible.value = true
  loading.value = true

  try {
    await loadDetail(record.id)
  } finally {
    loading.value = false
  }
}

/**
 * 切换详情数据
 */
const handleSwitchDetail = (index) => {
  loadDetail(dataList[index].id)
}

/**
 * 编辑水电表
 */
const handleEdit = () => {
  editDrawerRef.value.open(detailData.value)
}

/**
 * 加载详情数据
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const { result } = await queryWaterElectricityById({ id })
    if (result) {
      detailData.value = result
    }
  } finally {
    loading.value = false
  }
}

/**
 * 删除水电表
 */
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除"${detailData.value.name}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteWaterElectricity({ id: detailData.value.id })
      message.success('删除成功')
      visible.value = false
      emits('refresh')
    }
  })
}

/**
 * 刷新当前详情数据
 */
const refreshData = async () => {
  await loadDetail(detailData.value.id)
  emits('refresh')
}

defineExpose({ open })
</script>
