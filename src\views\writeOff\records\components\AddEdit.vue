<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    title="收付款核销"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
  >
    <div class="flex">
      <div class="w-[71%] pr-[40px] !h-[calc(100vh-185px)] overflow-auto no-scrollbar">
        <a-form
          :model="ruleForm"
          ref="formRef"
          :rules="rules"
          :label-col="{ style: { width: '120px' } }"
          autocomplete="off"
        >
          <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="核销方案" name="consumedType">
                <dict-select
                  v-model="ruleForm.consumedType"
                  placeholder="请选择核销方案"
                  code="CT_BAS_AssetsType"
                ></dict-select>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="政府记录优先对冲" name="isHedging">
                <a-switch v-model:checked="ruleForm.isHedging" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="核销日期" name="consumedTime">
                <a-date-picker
                  class="w-[100%]"
                  v-model:value="ruleForm.consumedTime"
                  picker="date"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择核销日期"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <div class="flex justify-between items-center mb-[12px] mt-[16px]">
            <h2 class="text-[16px] font-bold">选择收付款记录</h2>
            <a-button type="primary" ghost>添加</a-button>
          </div>
          <div class="mb-[20px]" v-for="(item, index) in ruleForm.consumedRecordEntryList" :key="index">
            <div class="flex justify-between items-center mb-[10px]">
              <span class="a-icon-remove text-[20px] text-[#8992A3]"></span>
              <a-select class="w-[96%]" v-model:value="item.a" placeholder="请选择"></a-select>
            </div>
            <div
              class="flex flex-wrap gap-y-[12px] text-secondary p-[10px] rounded-[8px] border border-[#e6e9f0] bg-[#E6E9F0]"
            >
              <span class="w-[50%]">收付款时间：{{ item.name || '-' }}</span>
              <span class="w-[50%]">客户：{{ item.name || '-' }}</span>
              <span class="w-[50%]">实际来款人：{{ item.name || '-' }}</span>
              <span class="w-[50%]">收付款公司：{{ item.name || '-' }}</span>
              <span class="w-[50%]">合计收付金额：{{ item.name || '-' }}</span>
              <span class="w-[50%]">已核销金额：{{ item.name || '-' }}</span>
            </div>
            <div></div>
          </div>

          <div class="flex justify-between items-center mb-[12px] mt-[40px]">
            <h2 class="text-[16px] font-bold">拟核销应收账单</h2>
            <a-button type="primary" ghost>添加</a-button>
          </div>

          <div class="mb-[20px]" v-for="(item, index) in ruleForm.recordsList" :key="index">
            <div class="flex justify-between items-center">
              <span class="a-icon-remove text-[20px] text-[#8992A3] mb-[24px]"></span>
              <a-select class="w-[50%] !mb-[24px]" v-model:value="item.a" placeholder="请选择"></a-select>
              <a-form-item
                class="w-[46%] !pl-[40px]"
                label="本次核销(元)"
                :name="['recordsList', index, 'assetsType']"
                :rules="{
                  required: true,
                  message: '请输入本次核销',
                  trigger: 'blur'
                }"
              >
                <a-input-number class="!w-[100%]" v-model:value="item.b" placeholder="请输入本次核销"></a-input-number>
              </a-form-item>
            </div>
            <div
              class="flex flex-wrap gap-y-[12px] text-secondary p-[10px] rounded-[8px] border-[#e6e9f0] bg-[#E6E9F0]"
            >
              <span class="w-[50%]">合同：{{ item.name || '-' }}</span>
              <span class="w-[50%]">款项类型：{{ item.name || '-' }}</span>
              <span class="w-[50%]">租赁单元：{{ item.name || '-' }}</span>
              <span class="w-[50%]">应收时间：{{ item.name || '-' }}</span>
              <span class="w-[50%]">实际应收金额：{{ item.name || '-' }}</span>
              <span class="w-[50%]">已核销金额：{{ item.name || '-' }}</span>
            </div>
            <div></div>
          </div>
        </a-form>
      </div>
      <div class="w-[29%]">
        <div class="inline-block w-[100%] bg-[#E6E9F0] p-[16px] rounded-[8px]">
          <h1 class="text-[18px] text-[#1D335C] font-bold mb-[16px]">核销结果预览</h1>
          <div class="rounded-[8px] bg-[#fff] mb-[16px] p-[12px]">
            <h2 class="text-[16px] text-[#1D335C] font-bold mb-[12px]">收付款</h2>
            <div class="text-secondary">
              <div class="mb-[12px]">收付款总金额：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">已核销金额：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">剩余可核销金额：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">本次拟核销金额：{{ previewInfo.name || '-' }}</div>
              <div>核销后剩余金额：{{ previewInfo.name || '-' }}</div>
            </div>
          </div>
          <div class="rounded-[8px] bg-[#fff] p-[12px]">
            <h2 class="text-[16px] text-[#1D335C] font-bold mb-[12px]">应收账单</h2>
            <div class="text-secondary">
              <div class="mb-[12px]">轻工大厦A2012-租金-202：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">轻工大厦A2012-物业费-202：{{ previewInfo.name || '-' }}</div>
              <div>轻工大厦A2012-水费-202：{{ previewInfo.name || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleConfirm">核销</a-button>
      <a-button type="primary" ghost @click="handleStash">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>
<script setup>
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data) => {
  if (data) {
    Object.assign(ruleForm, data, {
      attachmentIds: data.attachmentIds || [],
      pcaCode: data.pcaCode.split(','),
      buildYear: String(data.buildYear || '')
    })
  }
  visible.value = true
  // loadMenuList()
}
defineExpose({ open })
const previewInfo = ref({})
const ruleForm = reactive({
  id: '',
  manageCompany: '',
  number: '',
  consumedPerson: '',
  consumedType: '',
  consumedTime: '',
  thisConsumedMark: '',
  remark: '',
  createBy: '',
  createTime: '',
  updateBy: '',
  updateTime: '',
  auditBy: '',
  auditTime: '',
  sourceBillId: '',
  sourceBillEntryId: '',
  ctrlUnit: '',
  consumedRecordEntryList: [
    {
      id: '',
      billType: '',
      billNumber: '',
      billDate: '',
      manageCompany: '',
      collectionCompany: '',
      customer: '',
      paymentType: '',
      leaseUnit: '',
      contractNum: '',
      operator: '',
      operatorDepart: '',
      serviceCenter: '',
      carportNum: '',
      park: '',
      thisConsumedAmt: '',
      parent: '',
      remark: '',
      sourceBillId: '',
      sourceBillEntryId: ''
    }
  ]
})
const rules = computed(() => {
  return {
    writeoffPlan: [{ required: true, message: '请选择核销方案', trigger: ['change'] }],
    writeoffDate: [{ required: true, message: '请选择核销日期', trigger: ['change'] }]
  }
})

// 提交
const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  // await requestFunc.value.submit({ ...submitRuleForm.value })
  // message.success('提交成功')
}

// 暂存
const handleStash = async () => {
  // await formRef.value.validate()
  // await requestFunc.value.submit({ ...submitRuleForm.value })
  // message.success('提交成功')
}
// 取消
const handleCancel = () => {
  formRef.value.clearValidate()
  visible.value = false
}
</script>
