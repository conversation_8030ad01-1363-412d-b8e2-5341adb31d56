import request from '@/apis/http'

export const page = (params) =>
  request({
    method: 'get',
    url: '/sys/position/list',
    params
  })

export const add = (data) =>
  request({
    method: 'post',
    url: '/sys/position/add',
    data
  })

export const edit = (data) =>
  request({
    method: 'post',
    url: '/sys/position/edit',
    data
  })

export const remove = (params) =>
  request({
    method: 'delete',
    url: '/sys/position/delete',
    params
  })
