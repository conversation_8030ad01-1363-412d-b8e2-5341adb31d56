<template>
  <div>
    <!-- 水电表格 -->
    <div class="mb-4 flex justify-end">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus mr-1"></i>
        添加
      </a-button>
    </div>
    <a-table
      :columns="waterElectricColumns"
      :data-source="waterElectricList"
      :scroll="{ x: 2000 }"
      :pagination="false"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="handleEdit">编辑</a>
            <a @click="handleDelete" class="text-red-500">删除</a>
          </a-space>
        </template>
        <template v-if="column.formulaField">
          <a @click="handleSelectFormula">
            {{ record[column.dataIndex] || '点击选择' }}
          </a>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
const props = defineProps({
  // 水电信息列表
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emits = defineEmits(['update:modelValue'])

const waterElectricList = computed({
  get: () => props.modelValue,
  set: (val) => emits('update:modelValue', val)
})

const waterElectricColumns = [
  { title: '名称', dataIndex: 'name', key: 'name', width: 200, fixed: 'left' },
  { title: '编码(表号)', dataIndex: 'code', key: 'code', width: 150 },
  { title: '类型', dataIndex: 'type', key: 'type', width: 100 },
  { title: '属性', dataIndex: 'property', key: 'property', width: 100 },
  { title: '倍率', dataIndex: 'ratio', key: 'ratio', width: 100 },
  { title: '单价', dataIndex: 'price', key: 'price', width: 100 },
  { title: '损耗量计算公式', dataIndex: 'lossFormula', key: 'lossFormula', width: 180, formulaField: true },
  { title: '单位分摊计算公式', dataIndex: 'unitShareFormula', key: 'unitShareFormula', width: 180, formulaField: true },
  {
    title: '公摊金额计算公式',
    dataIndex: 'publicShareFormula',
    key: 'publicShareFormula',
    width: 180,
    formulaField: true
  },
  { title: '减免金额计算公式', dataIndex: 'reductionFormula', key: 'reductionFormula', width: 180, formulaField: true },
  { title: '自用金额计算公式', dataIndex: 'selfUseFormula', key: 'selfUseFormula', width: 180, formulaField: true },
  {
    title: '不含税合计计算公式',
    dataIndex: 'noTaxTotalFormula',
    key: 'noTaxTotalFormula',
    width: 180,
    formulaField: true
  },
  { title: '税金计算公式', dataIndex: 'taxFormula', key: 'taxFormula', width: 180, formulaField: true },
  { title: '含税合计计算公式', dataIndex: 'taxTotalFormula', key: 'taxTotalFormula', width: 180, formulaField: true }
]

/**
 * 添加水电表信息
 */
const handleAdd = () => {}

/**
 * 编辑水电表信息
 */
const handleEdit = () => {}

/**
 * 删除水电表信息
 */
const handleDelete = () => {}

/**
 * 选择计算公式
 */
const handleSelectFormula = () => {}
</script>
