设置招标结果
<template>
  <a-modal
    v-model:open="visible"
    title="招标结果"
    width="560px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-radio-group v-model:value="form.status">
      <a-radio value="SuccessBidding">中标</a-radio>
      <a-radio value="FailedBidding">流标</a-radio>
    </a-radio-group>
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '68px' } }"
      label-align="left"
      class="!mt-[24px]"
    >
      <a-form-item label="中标客户" name="customer">
        <a-form-item-rest>
          <customer-select v-model="form.customer"></customer-select>
        </a-form-item-rest>
      </a-form-item>
    </a-form>
    <files-upload v-model="form.attachmentIds"></files-upload>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addLog } from '../apis'

const emit = defineEmits(['refresh'])

const visible = ref(false)
const open = (id) => {
  form.parent = id
  visible.value = true
}

const form = reactive({
  parent: '',
  status: 'SuccessBidding',
  customer: '',
  attachmentIds: ''
})
const rules = {
  customer: [{ required: true, message: '请选择中标客户', trigger: 'none' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    await addLog(form)
    handleCancel()
    message.success('处理成功')
    emit('refresh')
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.status = 'SuccessBidding'
  form.customer = ''
  form.attachmentIds = ''
  visible.value = false
}

defineExpose({ open })
</script>
