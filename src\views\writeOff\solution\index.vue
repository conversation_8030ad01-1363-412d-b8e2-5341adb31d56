<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex justify-between !my-[24px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <span class="a-icon-plus mr-[8px]"></span>
          新建
        </a-button>
        <a-button @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            <span>批量操作</span>
            <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item>
                <span class="primary-btn" @click="handleRemove(false)">删除</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form autocomplete="off" layout="inline" class="!ml-[40px]">
          <a-form-item label="搜索">
            <s-input
              v-model="search.schemeName"
              placeholder="搜索方案名称"
              class="ml-[10px] !w-[280px]"
              @input="handleInput"
            ></s-input>
          </a-form-item>
          <a-form-item>
            <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
          </a-form-item>
        </a-form>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleRemove(record)">删除</span>
        </template>
      </template>
    </a-table>
    <add-edit ref="addEditRef" @load-data="onTableChange"></add-edit>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import AddEdit from './components/AddEdit.vue'
import { Modal, message } from 'ant-design-vue'
import { getPage, del, exportExcel, importExcel } from './apis'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { renderBoolean } from '@/utils/render'
onMounted(() => {
  onTableChange()
})
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const search = ref({
  manageCompany: '',
  schemeName: '',
  schemeNumber: ''
})
const searchList = reactive([
  { label: '公司', name: 'manageCompany', type: 'deptTree', placeholder: '请选择管理公司' },
  { label: '方案编码', name: 'schemeNumber', type: 'input', placeholder: '请输入方案编码' }
])
const defaultColumns = [
  { title: '核销方案编码', dataIndex: 'schemeNumber', width: 150, fixed: true },
  { title: '方案名称', dataIndex: 'schemeName' },
  { title: '公司', dataIndex: 'manageCompany_dictText' },
  { title: '归集公司相同', dataIndex: 'collectionCompanySame', customRender: ({ text }) => renderBoolean(text) },
  { title: '客户相同', dataIndex: 'customerSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '款项类型相同	', dataIndex: 'paymentTypeSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '租赁单元相同	', dataIndex: 'leaseUnitSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '合同号相同', dataIndex: 'contractNumberSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '业务部门相同', dataIndex: 'operatorDepartSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '业务员相同', dataIndex: 'operatorSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '服务处相同', dataIndex: 'serviceCenterSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '停车场相同', dataIndex: 'parkSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '车位号相同', dataIndex: 'carportNumSame', customRender: ({ text }) => renderBoolean(text) },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
// 编辑
const rowEdit = (row) => {
  // await getAttachmentIdsByBoId(row.id)
  addEditRef?.value.open(row)
}
// 批量删除
const handleRemove = (data) => {
  Modal.confirm({
    title: '提示',
    content: data ? '确认删除当前核销方案？' : '确认批量删除选中核销方案？',
    centered: true,
    onOk: async () => {
      await del(data ? data.id : selectedRowKeys.value.join(','))
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('楼栋数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>
