<template>
  <a-drawer
    v-model:open="visible"
    class="project-detail-drawer common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitchProject(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchProject(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEditProject">编辑</span>
          <span class="primary-btn" @click="handleProjectStatus">
            {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
          </span>
          <span class="primary-btn" @click="handleDeleteProject">删除</span>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <span class="bg-[#edfbe2] rounded-[8px] text-success leading-[28px] px-[8px]" v-if="detail.status === 'ENABLE'">
          已启用
        </span>
        <span
          class="bg-[#e6e9f0] rounded-[8px] text-tertiary leading-[28px] px-[8px]"
          v-if="detail.status === 'DISABLE'"
        >
          已禁用
        </span>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.updateBy_dictText }} 提交于{{ detail.updateTime }}</span>
      </div>
      <div>
        <h2 class="text-[16px] font-bold mb-[12px]">项目基础信息</h2>
        <a-row :gutter="20" class="text-secondary">
          <a-col :span="12">项目名称: {{ detail.name }}</a-col>
          <a-col :span="12">公司: {{ detail.company_dictText }}</a-col>
        </a-row>
        <div class="text-secondary mt-[12px]">备注: {{ detail.remark }}</div>
      </div>
      <div class="flex items-center justify-between mt-[40px] mb-[12px]">
        <h2 class="text-[16px] font-bold">楼栋信息</h2>
        <a-dropdown>
          <a-button type="primary" size="medium">
            添加楼栋
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="handleAddBuilding">手动添加</a-menu-item>
              <a-menu-item @click="handleImportBuilding">导入</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div class="flex gap-[16px]" v-if="buildingList.length">
        <ul class="w-[240px]">
          <li
            v-for="item in buildingList"
            :key="item.id"
            class="building-item"
            :class="{ active: currentBuilding.id === item.id }"
            @click="handleSwitchBuilding(item)"
          >
            <div class="flex-1 overflow-hidden">
              <h4 class="line-clamp-1" :title="item.name">{{ item.name }}</h4>
              <small>{{ item.number }}</small>
            </div>
            <span
              class="bg-[#edfbe2] rounded-[8px] text-success leading-[28px] px-[8px]"
              v-if="item.status === 'ENABLE'"
            >
              已启用
            </span>
            <span
              class="bg-[#e6e9f0] rounded-[8px] text-tertiary leading-[28px] px-[8px]"
              v-if="item.status === 'DISABLE'"
            >
              已禁用
            </span>
          </li>
        </ul>
        <section class="floor-section">
          <div class="flex items-center justify-between p-[16px] border-0 border-b border-solid border-[#e6e9f0]">
            <strong class="text-[16px]">{{ currentBuilding.name }}: 共{{ floorList.length }}楼层</strong>
            <div class="flex">
              <a-dropdown>
                <a-button type="primary" size="medium">
                  添加楼层
                  <i class="a-icon-arrow-down ml-[8px]"></i>
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleAddFloor">手动添加</a-menu-item>
                    <a-menu-item @click="handleImportFloor">导入</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
              <a-dropdown>
                <a-button size="medium">
                  更多
                  <i class="a-icon-arrow-down ml-[8px]"></i>
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleEditBuilding">编辑</a-menu-item>
                    <a-menu-item @click="handleBuildingStatus">
                      {{ currentBuilding.status === 'ENABLE' ? '禁用' : '启用' }}
                    </a-menu-item>
                    <a-menu-item @click="handleDeleteBuilding">删除</a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
          <a-table :data-source="floorList" :columns="columns" :loading="tableLoading" :pagination="false">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'leaseUnitCount'">
                <span v-if="record.leaseUnitCount === 0">0</span>
                <span class="primary-btn" v-else>查看({{ record.leaseUnitCount }})</span>
              </template>
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="handleEditFloor(record)">编辑</span>
                <a-popconfirm
                  title="是否确认删除该楼层？"
                  ok-text="确认"
                  cancel-text="取消"
                  @confirm="handleRemoveFloor(record)"
                >
                  <span class="primary-btn">删除</span>
                </a-popconfirm>
              </template>
            </template>
          </a-table>
        </section>
      </div>
      <a-empty description="暂未添加楼栋信息" v-else></a-empty>
    </a-spin>
  </a-drawer>
  <edit-building ref="editBuildingRef" :project-id="detail.id" @refresh="loadBuildingList" />
  <edit-floor
    ref="editFloorRef"
    :building-id="currentBuilding.id"
    @refresh="loadFloorList(currentBuilding.id)"
  ></edit-floor>
  <common-import
    ref="commonImportRef"
    key="1"
    modal-title="批量导入楼栋"
    :download-fn="() => exportExcel('楼栋数据导入模板.xls', { id: 0 })"
    :upload-fn="importExcel"
    @refresh="loadBuildingList"
  ></common-import>
  <common-import
    ref="importFloorRef"
    key="2"
    modal-title="批量导入楼层"
    :download-fn="() => exportFloorExcel('楼层数据导入模板.xls', { id: 0 })"
    :upload-fn="importFloorExcel"
    @refresh="loadFloorList(currentBuilding.id)"
  ></common-import>
</template>

<script setup>
import { projectDetail, queryBuilding, deleteProject, updateStatus } from '../apis.js'
import {
  queryFloor,
  updateStatus as updateBuildingStatus,
  deleteBuilding,
  importExcel,
  exportExcel
} from '@/views/building/apis/building.js'
import {
  deleteFloor,
  exportExcel as exportFloorExcel,
  importExcel as importFloorExcel
} from '@/views/building/apis/floor.js'
import { message, Modal } from 'ant-design-vue'
import EditBuilding from '@/views/building/components/EditBuilding.vue'
import EditFloor from '@/views/building/components/EditFloor.vue'

// dataList: 外面表格数据，用于上一条/下一条
const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const currentIndex = ref(0)
const handleSwitchProject = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  currentBuilding.id = ''
  loadDetail(dataList[index].id)
}

const emit = defineEmits(['editProject', 'refresh'])

const visible = ref(false)
const open = (id) => {
  visible.value = true
  loadDetail(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const loading = ref(false)
const detail = reactive({
  id: '',
  name: '',
  company_dictText: '',
  number: '',
  remark: '',
  updateTime: '',
  updateBy_dictText: '',
  status: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await projectDetail({ id })
  Object.assign(detail, result)
  await loadBuildingList()
  loading.value = false
}

const handleEditProject = () => {
  visible.value = false
  emit('editProject', detail)
}

const handleProjectStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该项目？`,
    content: '',
    centered: true,
    onOk: async () => {
      await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(detail.status === 'ENABLE' ? '已禁用' : '启用成功')
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDeleteProject = () => {
  Modal.confirm({
    title: '确认删除该项目？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteProject({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const buildingList = ref([])
const loadBuildingList = async () => {
  const { result } = await queryBuilding({ id: detail.id })
  buildingList.value = result
  if (!currentBuilding.id) {
    Object.assign(currentBuilding, buildingList.value[0])
    loadFloorList(currentBuilding.id)
  } else {
    loadFloorList(currentBuilding.id)
  }
}

const currentBuilding = reactive({
  id: '',
  name: '',
  status: ''
})

const handleSwitchBuilding = (item) => {
  Object.assign(currentBuilding, item)
  loadFloorList(currentBuilding.id)
}

const tableLoading = ref(false)
const columns = [
  { title: '楼层名称', dataIndex: 'name' },
  { title: '租赁单元', dataIndex: 'leaseUnitCount' },
  { title: '操作', dataIndex: 'action' }
]
const floorList = ref([])
const loadFloorList = async (id) => {
  tableLoading.value = true
  const { result } = await queryFloor({ id })
  floorList.value = result
  tableLoading.value = false
}

const editBuildingRef = ref()
const handleAddBuilding = () => {
  editBuildingRef.value.open()
}
const handleEditBuilding = () => {
  editBuildingRef.value.open(currentBuilding.id)
}

const commonImportRef = ref()
const handleImportBuilding = () => {
  commonImportRef.value.open()
}

const handleBuildingStatus = () => {
  Modal.confirm({
    title: `确认${currentBuilding.status === 'ENABLE' ? '禁用' : '启用'}该楼栋？`,
    content: currentBuilding.status === 'ENABLE' ? '楼栋禁用后将无法再被使用，但不影响已创建的数据。' : '',
    centered: true,
    onOk: async () => {
      await updateBuildingStatus({
        ids: currentBuilding.id,
        status: currentBuilding.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(currentBuilding.status === 'ENABLE' ? '已禁用楼栋' : '已启用楼栋')
      currentBuilding.status = currentBuilding.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      loadBuildingList()
    }
  })
}
const handleDeleteBuilding = () => {
  Modal.confirm({
    title: '确认删除该楼栋？',
    content: '',
    centered: true,
    onOk: async () => {
      const index = buildingList.value.findIndex((item) => item.id === currentBuilding.id)
      await deleteBuilding({ ids: currentBuilding.id })
      message.success('删除成功')
      await loadBuildingList()
      if (buildingList.value.length) {
        Object.assign(currentBuilding, buildingList.value[index] || buildingList.value[index - 1])
      } else {
        Object.assign(currentBuilding, {
          id: '',
          name: '',
          status: ''
        })
      }
    }
  })
}

const editFloorRef = ref()
const handleAddFloor = () => {
  editFloorRef.value.open()
}
const handleEditFloor = (data) => {
  editFloorRef.value.open({
    id: data.id,
    name: data.name,
    status: data.status
  })
}

const handleRemoveFloor = async (data) => {
  await deleteFloor({ ids: data.id })
  message.success('已删除')
  loadFloorList(currentBuilding.id)
}

const importFloorRef = ref()
const handleImportFloor = () => {
  importFloorRef.value.open()
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.project-detail-drawer {
  .building-item {
    border-radius: 8px;
    border: 1px solid #e6e9f0;
    background-color: #f7f8fa;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    transition:
      border-color 0.2s,
      background-color 0.2s;
    cursor: pointer;
    &:hover {
      border-color: var(--color-primary);
    }
    &.active {
      border-color: var(--color-primary);
      background-color: #eaf0fe;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .floor-section {
    flex: 1;
    border: 1px solid #e6e9f0;
    border-radius: 8px;
    overflow: hidden;
  }
  .ant-table {
    border: none;
    overflow: visible;
    .ant-table-cell {
      border-bottom: 1px solid #f0f0f0 !important;
    }
  }
}
</style>
