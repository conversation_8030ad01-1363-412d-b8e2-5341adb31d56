<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex mt-[24px] mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus mr-1"></i>
        新建
      </a-button>
      <a-button @click="handleImport">
        <i class="a-icon-import-right mr-1"></i>
        导入
      </a-button>
      <a-button :loading="exportLoading" @click="handleExport">
        <i class="a-icon-export-right mr-1"></i>
        导出
      </a-button>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <s-input
        v-model="searchParams.number"
        placeholder="搜索编号"
        class="ml-[40px] !w-[280px]"
        @input="handleSearch"
      ></s-input>
      <filter-more
        :params="searchParams"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      :scroll="{ y: tableHeight }"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'changeDetail'">变为{{ record.destStatus_dictText }}</template>
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_AuditStatus" type="dot"></status-tag>
        </template>
        <template v-if="column.dataIndex === 'description'">
          <div class="line-clamp-2" :title="record.description">{{ record.description }}</div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleDetail(record)">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit" @click="handleEdit(record)" v-if="record.status !== 'AUDITING'">
                  编辑
                </a-menu-item>
                <a-menu-item key="submit" @click="handleSubmit(record)" v-if="record.status === 'TEMP'">
                  提交
                </a-menu-item>
                <a-menu-item key="back" v-if="record.status !== 'TEMP'">撤回</a-menu-item>
                <a-menu-item key="delete" @click="handleDelete(record)" v-if="record.status === 'TEMP'">
                  删除
                </a-menu-item>
                <a-menu-item key="showAudit" v-if="record.status === 'AUDITOK'">查看审批</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('租赁单元状态变更申请导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
    ></common-import>

    <edit-lease-unit-state-change ref="editDrawerRef" @refresh="onTableChange"></edit-lease-unit-state-change>
    <lease-unit-state-change-detail ref="detailDrawerRef" :data-list="list"></lease-unit-state-change-detail>
  </div>
</template>

<script setup>
import { Modal, message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import EditLeaseUnitStateChange from './components/EditLeaseUnitStateChange.vue'
import LeaseUnitStateChangeDetail from './components/LeaseUnitStateChangeDetail.vue'
import {
  getLeaseUnitStateChangeReqBillList,
  getLeaseUnitStateChangeReqBillById,
  deleteLeaseUnitStateChangeReqBill,
  submitLeaseUnitStateChangeReqBill,
  importExcel,
  exportExcel
} from './apis'

const route = useRoute()

const editDrawerRef = ref()
const detailDrawerRef = ref()
const commonImportRef = ref()

const exportLoading = ref(false)
let timer

const searchParams = reactive({
  number: undefined,
  manageCompany: undefined,
  status: undefined,
  destStatus: undefined,
  bizDate: undefined,
  createBy: undefined,
  remark: undefined
})

const searchList = reactive([
  { label: '管理公司', name: 'manageCompany', type: 'company-select', placeholder: '请选择管理公司' },
  {
    label: '目标状态',
    name: 'destStatus',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_LeaseUnit_BizStatus',
    placeholder: '请选择目标状态'
  },
  {
    label: '单据状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择单据状态'
  },
  { label: '业务日期', name: 'bizDate', type: 'date', placeholder: '请选择业务日期' },
  { label: '提交人', name: 'createBy', type: 'user-select', placeholder: '请选择提交人' },
  { label: '变更说明', name: 'remark', type: 'input', placeholder: '请输入变更说明' }
])

const pageTitle = computed(() => route.meta.title)

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true },
  { title: '单元变更信息', dataIndex: 'changeDetail', width: 160, ellipsis: true },
  { title: '状态', dataIndex: 'status', width: 120 },
  { title: '业务日期', dataIndex: 'bizDate', width: 120 },
  { title: '提交人', dataIndex: 'createBy_dictText', width: 120 },
  { title: '变更说明', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getLeaseUnitStateChangeReqBillList)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

/**
 * 新建状态变更单
 */
const handleAdd = () => {
  editDrawerRef.value.open()
}

/**
 * 查看详情
 */
const handleDetail = (record) => {
  detailDrawerRef.value.open(record)
}

/**
 * 编辑状态变更单
 */
const handleEdit = (record) => {
  editDrawerRef.value.open(record)
}

/**
 * 提交状态变更单
 */
const handleSubmit = (record) => {
  Modal.confirm({
    title: '确认提交',
    content: `确定要提交单据编号为 "${record.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      // 先获取最新的租赁单元列表
      const { result } = await getLeaseUnitStateChangeReqBillById({ id: record.id })
      if (result && result.length > 0) {
        record.leaseUnitStateChangeReqBillEntryList = result
      }
      // 提交变更申请
      await submitLeaseUnitStateChangeReqBill(record)
      message.success('提交成功')
      onTableChange()
    }
  })
}

/**
 * 删除状态变更单
 */
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除单据编号为 "${record.number}" 的记录吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      await deleteLeaseUnitStateChangeReqBill({ id: record.id })
      message.success('删除成功')
      onTableChange()
    }
  })
}

/**
 * 导入
 */
const handleImport = () => {
  commonImportRef.value.open()
}

/**
 * 导出
 */
const handleExport = async () => {
  if (exportLoading.value) return
  exportLoading.value = true
  await exportExcel('租赁单元状态变更申请清单.xls', searchParams)
  message.success('导出成功')
}

/**
 * 搜索输入防抖处理
 */
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格变化事件处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

onMounted(() => {
  onTableChange()
})
</script>
