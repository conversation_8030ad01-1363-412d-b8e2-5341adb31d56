<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex items-center">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新增
        </a-button>
        <a-button @click="handleDeleteBatch" v-show="selectedRowKeys.length">批量删除</a-button>
        <span class="shrink-0 ml-[16px] mr-[8px] text-[14px]">菜单名称</span>
        <a-input v-model:value="params.name" placeholder="搜索菜单名称" @input="handleInput">
          <template #prefix>
            <i class="a-icon-search text-primary"></i>
          </template>
        </a-input>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="loading"
      :pagination="false"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ y: tableHeight }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'icon'">
          <i :class="record.icon" class="text-[16px]"></i>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-dropdown v-if="record.menuType !== menuType.PERMISSION">
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleRemove(record)">
                  <span class="primary-btn">删除</span>
                </a-menu-item>
                <a-menu-item @click="handleAddChildren(record)">
                  <span class="primary-btn">添加下级</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <span class="primary-btn" @click="handleRemove(record)" v-else>删除</span>
        </template>
      </template>
    </a-table>
    <edit-menu ref="editMenuRef" @load-data="loadData"></edit-menu>
  </div>
</template>

<script setup>
import { getMenuList, deleteMenu, deleteBatch } from './apis'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import EditMenu from './components/Edit.vue'
import { message, Modal } from 'ant-design-vue'
import { renderDict } from '@/utils/render'
import { useDictStore } from '@/store/modules/dict'

const { menuType } = useDictStore()

const route = useRoute()
const pageTitle = computed(() => route.meta.title)

const params = reactive({
  name: '',
  order: '',
  column: ''
})
const defaultColumns = [
  { title: '菜单名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '菜单类型', dataIndex: 'menuType', width: 150, customRender: ({ text }) => renderDict(text, 'menu_type') },
  { title: '图标', dataIndex: 'icon', width: 100 },
  { title: '路径', dataIndex: 'url' },
  { title: '排序', dataIndex: 'sortNo', width: 100 },
  { title: '操作', dataIndex: 'action', fixed: 'right', width: 120 }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { tableHeight } = usePageTable(null, null, false)

const loading = ref(false)
const list = ref([])
const loadData = async () => {
  loading.value = true
  const { result } = await getMenuList(params)
  list.value = result
  loading.value = false
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    loadData()
  }, 600)
}

const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id', true)

const editMenuRef = ref()
const handleAdd = () => {
  editMenuRef.value.open()
}
const handleEdit = (row) => {
  editMenuRef.value.open(row)
}

const handleDeleteBatch = () => {
  Modal.confirm({
    title: '系统提示',
    content: '是否确认删除选中菜单？',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: selectedRowKeys.value.join(',') })
      message.success('删除成功')
      loadData()
    }
  })
}

const handleRemove = (row) => {
  Modal.confirm({
    title: '系统提示',
    content: '是否确认删除该菜单？',
    centered: true,
    onOk: async () => {
      await deleteMenu({ id: row.id })
      message.success('删除成功')
      loadData()
    }
  })
}

const handleAddChildren = (row) => {
  editMenuRef.value.open({
    id: '',
    menuType: 1,
    name: '',
    url: '',
    component: '',
    componentName: null,
    icon: '',
    sortNo: '1',
    route: true,
    hidden: false,
    hideTab: false,
    keepAlive: false,
    alwaysShow: false,
    internalOrExternal: false,
    redirect: '',
    parentId: row.id
  })
}

onMounted(() => {
  loadData()
})
</script>
