<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑职务' : '新增职务'"
    width="600px"
    wrap-class-name="common-modal"
    :confirm-loading="confirmLoading"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '84px' } }" autocomplete="off">
      <a-form-item label="职务名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入职务名称" :maxlength="20" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { add, edit } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const open = (data) => {
  if (data) {
    Object.assign(form, data)
  }
  visible.value = true
}
const form = reactive({
  id: '',
  name: ''
})

const rules = {
  name: [{ required: true, message: '请输入职务名称', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    handleCancel()
    emits('refresh')
  } finally {
    confirmLoading.value = false
  }
}
const handleCancel = () => {
  form.id = ''
  form.name = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
