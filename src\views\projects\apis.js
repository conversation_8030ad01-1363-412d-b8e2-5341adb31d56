import request from '@/apis/http'

export const projectPage = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyProject/list',
    params
  })
}

export const projectDetail = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyProject/queryById',
    params
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyProject/updateEnableDisableStatus',
    data
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/wyProject/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyProject/importExcel',
    data
  })
}

export const addProject = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyProject/add',
    data
  })
}

export const editProject = (data) => {
  return request({
    method: 'post',
    url: '/bas/wyProject/edit',
    data
  })
}

export const deleteProject = (params) => {
  return request({
    method: 'delete',
    url: '/bas/wyProject/deleteBatch',
    params
  })
}

export const queryBuilding = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyProject/queryWyBuildingByMainId',
    params
  })
}
