import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/contractmanage/contract/list',
    params
  })
}

export const list = (params) => {
  return request({
    method: 'get',
    url: '/contractmanage/contract/queryList',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/contractmanage/contract/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/importExcel',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/unAudit',
    data
  })
}

export const save = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/contractmanage/contract/deleteBatch',
    params
  })
}

export const abort = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/terminate',
    data
  })
}

export const cancelAbort = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/unTerminate',
    data
  })
}

// 合同租赁单元是否包含重叠查询
export const queryOverlayContractList = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/queryOverlayContractList',
    data
  })
}

// 明细账单查询
export const queryBillList = (data) => {
  return request({
    method: 'post',
    url: '/contractmanage/contract/calculRentLogic',
    data
  })
}

// 合同其他款项明细账单分录-通过主表ID查询
export const queryOtherBillList = (params) => {
  return request({
    method: 'get',
    url: '/contractmanage/contract/queryContractOtherDetailBillsByMainId',
    params
  })
}

// 合同租赁单元分录-通主表ID查询
export const queryContractLeaseUnits = (params) => {
  return request({
    method: 'get',
    url: '/contractmanage/contract/queryContractLeaseUnitsByMainId',
    params
  })
}

// 合同款项分录-通主表ID查询
export const queryContractLeaseFunds = (params) => {
  return request({
    method: 'get',
    url: '/contractmanage/contract/queryContractLeaseFundsByMainId',
    params
  })
}

// 合同减免分录-通主表ID查询
export const queryContractFeeReduce = (params) => {
  return request({
    method: 'get',
    url: '/contractmanage/contract/queryContractFeeReduceReqsByMainId',
    params
  })
}

// 获取合同款项类型列表
export const getContractFundTypeList = (params) => {
  return request({
    method: 'get',
    url: '/bas/paymentType/list',
    params
  })
}
