<template>
  <div>
    <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">租赁信息</h4>
    <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
      <a-form-item label="使用类型" name="useType">
        <dict-select v-model="formData.useType" placeholder="使用类型" code="CT_BAS_UseType"></dict-select>
      </a-form-item>
      <a-form-item label="租赁面积(m²)" name="leaseArea">
        <a-input-number v-model:value="formData.leaseArea" :precision="2" style="width: 100%" addon-after="m²" />
      </a-form-item>
      <a-form-item label="租赁用途" name="leaseUse">
        <dict-select v-model="formData.leaseUse" placeholder="租赁用途" code="CT_BAS_LeaseUse"></dict-select>
      </a-form-item>
      <a-form-item label="片区管理员" name="areaManager">
        <a-form-item-rest>
          <user-select v-model="formData.areaManager" placeholder="请选择片区管理员" title="请选择片区管理员" />
        </a-form-item-rest>
      </a-form-item>
      <a-form-item label="生效日期" name="effectDate">
        <a-date-picker v-model:value="formData.effectDate" value-format="YYYY-MM-DD" style="width: 100%" />
      </a-form-item>
      <a-form-item label="到期日期" name="expireDate">
        <a-date-picker v-model:value="formData.expireDate" value-format="YYYY-MM-DD" style="width: 100%" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup>
const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const formRef = ref()

const rules = {
  useType: [{ required: true, message: '请选择使用类型', trigger: 'change' }],
  leaseArea: [
    { required: true, message: '请输入租赁面积', trigger: 'blur' },
    { type: 'number', min: 0, message: '租赁面积不能为负数', trigger: 'blur' }
  ],
  leaseUse: [{ required: true, message: '请选择租赁用途', trigger: 'change' }],
  effectDate: [{ required: true, message: '请选择生效日期', trigger: 'change' }],
  expireDate: [
    { required: true, message: '请选择到期日期', trigger: 'change' },
    {
      validator: (_rule, value) => {
        if (!value || !props.formData.effectDate) return Promise.resolve()
        return value >= props.formData.effectDate ? Promise.resolve() : Promise.reject('到期日期不能早于生效日期')
      },
      trigger: 'change'
    }
  ]
}

/**
 * 验证表单
 */
const validate = () => {
  return formRef.value?.validate()
}

/**
 * 重置表单
 */
const resetFields = () => {
  formRef.value?.resetFields()
}

defineExpose({
  validate,
  resetFields
})
</script>
