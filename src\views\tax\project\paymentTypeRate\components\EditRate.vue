<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑款项税率信息' : '新建款项税率信息'"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '100px' } }" autocomplete="off">
        <a-form-item label="款项类型" name="paymentType">
          <a-select v-model:value="form.paymentType" placeholder="请选择款项类型">
            <a-select-option v-for="p in paymentTypeList" :key="p.id" :value="p.id">{{ p.name }}</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="开票税率" name="invoiceRate">
          <a-input-number
            v-model:value="form.invoiceRate"
            :min="0"
            :max="100"
            addon-after="%"
            placeholder="请输入开票税率"
            class="!w-full"
          />
        </a-form-item>
        <a-form-item label="开票类型" name="invoiceType">
          <dict-select v-model="form.invoiceType" code="CT_BASE_ENUM_Customer_InvoiceType"></dict-select>
        </a-form-item>
        <a-form-item label="发票类型" name="invoiceForm">
          <dict-select
            v-model="form.invoiceForm"
            code="CT_BASE_ENUM_ReceiveItemPaymentTypeRate_InvoiceForm"
          ></dict-select>
        </a-form-item>
        <a-form-item label="简易征收类别" name="simpleCollectionType">
          <dict-select
            v-model="form.simpleCollectionType"
            code="CT_BASE_ENUM_ReceiveItemPaymentTypeRate_SimpleCollectionType"
          ></dict-select>
        </a-form-item>
        <a-form-item label="优惠政策编码" name="policyCode">
          <dict-select
            v-model="form.policyCode"
            code="CT_BASE_ENUM_ReceiveItemPaymentTypeRate_PolicyCode"
          ></dict-select>
        </a-form-item>
        <a-form-item label="计量单位" name="measureUnit">
          <dict-select v-model="form.measureUnit" code="CT_BAS_MeasureUnit"></dict-select>
        </a-form-item>
        <a-form-item label="开票时点" name="invoiceTime">
          <dict-select
            v-model="form.invoiceTime"
            code="CT_BASE_ENUM_ReceiveItemPaymentTypeRate_InvoiceTime"
          ></dict-select>
        </a-form-item>
        <a-form-item label="发票品名" name="invoiceName">
          <a-input v-model:value="form.invoiceName" placeholder="请输入发票品名" :maxlength="50"></a-input>
        </a-form-item>
        <a-form-item label="备注" name="remark" class="!w-full">
          <a-textarea
            v-model:value="form.remark"
            placeholder="请输入备注(选填)"
            show-count
            :maxlength="200"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { add, edit, detail } from '../apis.js'
import { message } from 'ant-design-vue'
import { list as queryPaymentType } from '@/views/paymentType/apis.js'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (manageCompany, id) => {
  visible.value = true
  form.manageCompany = manageCompany
  loadPaymentType()
  if (id) {
    loadDetail(id)
  }
}

const paymentTypeList = ref([])
const loadPaymentType = async () => {
  const { result } = await queryPaymentType({ manageCompany: form.manageCompany })
  paymentTypeList.value = result
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.paymentType = result.paymentType
  form.invoiceRate = result.invoiceRate
  form.invoiceType = result.invoiceType
  form.invoiceForm = result.invoiceForm
  form.simpleCollectionType = result.simpleCollectionType
  form.policyCode = result.policyCode
  form.measureUnit = result.measureUnit
  form.invoiceTime = result.invoiceTime
  form.invoiceName = result.invoiceName
  form.remark = result.remark
  loading.value = false
}

const form = reactive({
  id: '',
  paymentType: '',
  invoiceRate: '',
  invoiceType: '',
  invoiceForm: '',
  simpleCollectionType: '',
  policyCode: '',
  measureUnit: '',
  invoiceTime: '',
  invoiceName: '',
  remark: ''
})

const rules = {
  paymentType: [{ required: true, message: '请选择款项类型', trigger: 'change' }],
  invoiceRate: [{ required: true, message: '请输入开票税率', trigger: 'change' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    form.id ? await edit(params) : await add(params)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.paymentType = ''
  form.invoiceRate = ''
  form.invoiceType = ''
  form.invoiceForm = ''
  form.simpleCollectionType = ''
  form.policyCode = ''
  form.measureUnit = ''
  form.invoiceTime = ''
  form.invoiceName = ''
  form.remark = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
