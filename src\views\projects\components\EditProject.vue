<template>
  <a-drawer
    v-model:open="visible"
    class="edit-project-drawer common-drawer"
    :title="form.id ? '编辑项目' : '新建项目'"
    :mask-closable="false"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '84px' } }" autocomplete="off">
      <h2 class="text-[16px] font-bold mb-[20px]">项目基础信息</h2>
      <div class="flex gap-[20px]">
        <a-form-item label="项目名称" name="name" class="flex-1">
          <a-input v-model:value="form.name" placeholder="请输入项目名称" :maxlength="20"></a-input>
        </a-form-item>
        <a-form-item label="公司" name="company" class="flex-1">
          <dept-tree-select v-model="form.company" placeholder="请选择公司" type="company"></dept-tree-select>
        </a-form-item>
      </div>
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="form.remark"
          placeholder="请输入备注(选填)"
          show-count
          :maxlength="500"
          :auto-size="{ minRows: 5, maxRows: 5 }"
        ></a-textarea>
      </a-form-item>
    </a-form>
    <template v-if="!form.id">
      <h2 class="text-[16px] font-bold mb-[20px]">楼栋信息</h2>
      <div class="flex" v-if="form.wyBuildingPageList.length">
        <aside class="w-[240px]">
          <draggable v-model="form.wyBuildingPageList" handle=".a-icon-move" item-key="id">
            <template #item="{ element, index }">
              <div class="building-item" :class="{ active: currentBuilding.id === element.id }">
                <i
                  class="a-icon-move cursor-move text-tertiary mr-[8px] text-[20px]"
                  v-show="form.wyBuildingPageList.length > 1"
                ></i>
                <a-popconfirm title="是否确认移除？" @confirm="handleRemoveBuilding(index)">
                  <div>
                    <i
                      class="a-icon-remove cursor-pointer text-tertiary mr-[12px] text-[20px] hover:text-error transition-colors"
                    ></i>
                  </div>
                </a-popconfirm>
                <span class="line-clamp-1 flex-1" @click="handleBuildingClick(element)">
                  {{ element.name }}
                </span>
              </div>
            </template>
          </draggable>
          <a-button class="w-full" type="primary" size="large" ghost @click="modalVisible = true">
            <i class="a-icon-plus"></i>
            添加楼栋
          </a-button>
        </aside>
        <div class="w-[1px] bg-[#e6e9f0] mx-[24px]"></div>
        <section class="flex-1">
          <div class="mb-[10px]">
            楼栋名称
            <span class="text-error">*</span>
          </div>
          <a-input
            v-model:value="currentBuilding.name"
            placeholder="请输入楼栋名称"
            :maxlength="50"
            show-count
          ></a-input>
          <div class="mt-[40px] mb-[16px] flex items-center justify-between">
            <span>楼层</span>
            <a-button type="primary" ghost @click="handleAddFloor">
              <i class="a-icon-plus"></i>
              增加楼层
            </a-button>
          </div>
          <draggable v-model="currentBuilding.wyFloorList" handle=".a-icon-move" item-key="id">
            <template #item="{ element, index }">
              <div class="flex items-center justify-between mb-[16px] last-of-type:mb-[0]">
                <i
                  class="a-icon-move cursor-move text-tertiary mr-[8px] text-[20px]"
                  v-show="currentBuilding.wyFloorList.length > 1"
                ></i>
                <a-popconfirm title="是否确认移除？" @confirm="handleRemoveFloor(index)">
                  <div>
                    <i class="a-icon-remove cursor-pointer text-tertiary mr-[12px] hover:text-error text-[20px]"></i>
                  </div>
                </a-popconfirm>
                <a-input v-model:value="element.name" class="flex-1" placeholder="请输入楼层名称"></a-input>
              </div>
            </template>
          </draggable>
        </section>
      </div>
      <div v-else>
        <a-button size="large" type="primary" ghost class="w-full" @click="modalVisible = true">
          <i class="a-icon-plus"></i>
          添加楼栋
        </a-button>
      </div>
    </template>
    <template #footer>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
    <a-modal
      v-model:open="modalVisible"
      title="楼栋信息"
      width="560px"
      wrap-class-name="common-modal"
      :mask-closable="false"
      centered
      @ok="handleModalConfirm"
      @cancel="handleModalCancel"
    >
      <a-form
        :model="buildingForm"
        ref="buildingFormRef"
        :rules="buildingRules"
        :label-col="{ style: { width: '84px' } }"
        autocomplete="off"
      >
        <a-form-item label="楼栋名称" name="name">
          <a-input v-model:value="buildingForm.name" placeholder="请输入楼栋名称" :maxlength="50" show-count></a-input>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-drawer>
</template>

<script setup>
import { addProject, editProject } from '../apis.js'
import draggable from 'vuedraggable'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (data) => {
  if (data) {
    Object.assign(form, {
      id: data.id,
      company: data.company,
      name: data.name,
      remark: data.remark
    })
  }
  visible.value = true
}

const form = reactive({
  id: '',
  company: '',
  name: '',
  remark: '',
  wyBuildingPageList: []
})

const rules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  company: [{ required: true, message: '请选择公司', trigger: 'change' }]
}

const formRef = ref()
const loading = ref(false)
const handleSubmit = async () => {
  if (loading.value) return
  await formRef.value.validate()
  const result = form.wyBuildingPageList.some((item) => {
    if (!item.wyFloorList.length) {
      message.warning(`楼栋${item.name}请添加楼层`)
      Object.assign(currentBuilding, item)
      return true
    }
    if (item.wyFloorList.some((i) => i.name.trim() === '')) {
      Object.assign(currentBuilding, item)
      message.warning(`楼栋${item.name}有楼层名称为空`)
      return true
    }
    return false
  })
  if (result) return
  try {
    loading.value = true
    const params = JSON.parse(JSON.stringify(form))
    if (form.id) {
      delete params.wyBuildingPageList
    } else {
      params.wyBuildingPageList = params.wyBuildingPageList.map((item) => {
        if (item.id.includes('.')) {
          item.id = ''
        }
        item.wyFloorList.forEach((i) => {
          if (i.id.includes('.')) {
            i.id = ''
          }
        })
        return item
      })
    }
    form.id ? await editProject(params) : await addProject(params)
    loading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    loading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.company = ''
  form.name = ''
  form.remark = ''
  form.wyBuildingPageList = []
  formRef.value.clearValidate()
  visible.value = false
}

const modalVisible = ref(false)
const buildingForm = reactive({
  name: ''
})
const buildingRules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
}
const buildingFormRef = ref()
const handleModalConfirm = async () => {
  await buildingFormRef.value.validate()
  const data = {
    id: Math.random().toString(),
    name: buildingForm.name,
    wyFloorList: []
  }
  form.wyBuildingPageList.push(data)
  Object.assign(currentBuilding, data)
  handleModalCancel()
}
const handleModalCancel = () => {
  buildingFormRef.value.resetFields()
  modalVisible.value = false
}

const handleRemoveBuilding = (index) => {
  form.wyBuildingPageList.splice(index, 1)
}

const handleBuildingClick = (data) => {
  if (data.id === currentBuilding.id) return
  if (currentBuilding.name.trim() === '') {
    message.warning('楼栋名称不可为空')
    return
  }
  if (currentBuilding.wyFloorList.length === 0) {
    message.warning('请添加楼层')
    return
  }
  if (currentBuilding.wyFloorList.some((i) => i.name.trim() === '')) {
    message.warning('楼层名称不可为空')
    return
  }
  Object.assign(currentBuilding, data)
}

const currentBuilding = reactive({
  id: '',
  name: '',
  wyFloorList: []
})

const handleAddFloor = () => {
  currentBuilding.wyFloorList.push({
    id: Math.random().toString(),
    name: ''
  })
}

const handleRemoveFloor = (index) => {
  currentBuilding.wyFloorList.splice(index, 1)
}

defineExpose({ open })
</script>

<style lang="less">
.edit-project-drawer {
  .building-item {
    display: flex;
    align-items: center;
    border: 1px solid #e6e9f0;
    background-color: #f7f8fa;
    border-radius: 8px;
    height: 40px;
    padding: 0 16px;
    margin-bottom: 16px;
    cursor: pointer;
    transition:
      border-color 0.2s,
      background-color 0.2s;
    &:hover {
      border-color: var(--color-primary);
    }
    &.active {
      border-color: var(--color-primary);
      background-color: #eaf0fe;
    }
  }
}
</style>
