<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center mt-[28px] mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新增
      </a-button>
      <a-form layout="inline" ref="formRef" class="!ml-[40px]" autocomplete="off">
        <a-form-item label="角色名称">
          <a-input v-model:value="params.roleName" placeholder="请输入角色名称" allow-clear @input="handleInput">
            <template #prefix><i class="a-icon-search text-primary"></i></template>
          </a-input>
        </a-form-item>
        <a-form-item label="角色编码">
          <a-input v-model:value="params.roleCode" placeholder="请输入角色编码" allow-clear @input="handleInput">
            <template #prefix><i class="a-icon-search text-primary"></i></template>
          </a-input>
        </a-form-item>
      </a-form>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'description'">
          <div class="line-clamp-2" :title="record.description">{{ record.description }}</div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <span class="primary-btn" @click="handleAuth(record)">授权</span>
          <a-popconfirm title="是否确认删除？" ok-text="确认" cancel-text="取消" @confirm="handleRemove(record)">
            <span class="primary-btn">删除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <edit-role ref="editRoleRef" @refresh="refreshData"></edit-role>
    <permission-config ref="permissionRef"></permission-config>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message } from 'ant-design-vue'
import { getRoleList, deleteRole } from './apis'
import EditRole from './components/EditRole.vue'
import PermissionConfig from './components/PermissionConfig.vue'

const route = useRoute()
const pageTitle = computed(() => route.meta.title)

const params = reactive({
  column: 'id',
  order: 'desc',
  roleName: '',
  roleCode: ''
})

const columns = [
  { title: '角色名称', dataIndex: 'roleName', width: 200, fixed: 'left' },
  { title: '角色编码', dataIndex: 'roleCode' },
  { title: '描述', dataIndex: 'description' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getRoleList)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const editRoleRef = ref()
const handleAdd = () => {
  editRoleRef.value.open()
}
const handleEdit = (row) => {
  editRoleRef.value.open({
    id: row.id,
    roleName: row.roleName,
    roleCode: row.roleCode,
    description: row.description
  })
}

const refreshData = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const handleRemove = async (data) => {
  await deleteRole({ id: data.id })
  message.success('删除成功')
  let pageNo = pagination.value.current
  if (pagination.value.pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

const permissionRef = ref()
const handleAuth = (data) => {
  permissionRef.value.open(data)
}

onMounted(() => {
  onTableChange()
})
</script>
