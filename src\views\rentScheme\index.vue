<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建招租方案
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <s-input
          v-model="params.name"
          placeholder="搜索名称"
          class="ml-[10px] !w-[280px]"
          @input="handleInput"
        ></s-input>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
      @expand="handleExpand"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleRemove(record)">删除</a-menu-item>
                <a-menu-item @click="handleViewCustomer(record)">查看中标客户</a-menu-item>
                <a-menu-item @click="handleViewContract(record)">查看中标合同</a-menu-item>
                <a-menu-item @click="handleSetResult(record)">招标结果</a-menu-item>
                <a-menu-item @click="handleAudit(record, true)">审核</a-menu-item>
                <a-menu-item @click="handleAudit(record, false)">反审核</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit-rent-scheme ref="editRentSchemeRef" @refresh="refresh"></edit-rent-scheme>
    <rent-scheme-detail
      ref="rentSchemeDetailRef"
      :data-list="list"
      @edit-rent-scheme="handleEdit"
      @refresh="refreshFromDetail"
    ></rent-scheme-detail>
    <set-result ref="setResultRef" @refresh="refresh"></set-result>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('招租方案导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit, logList } from './apis.js'
import EditRentScheme from './components/EditRentScheme.vue'
import RentSchemeDetail from './components/RentSchemeDetail.vue'
import SetResult from './components/SetResult.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag } from '@/utils/render'

const route = useRoute()

const pageTitle = computed(() => route.meta.title)

const params = reactive({
  id: undefined,
  number: undefined,
  title: undefined,
  status: undefined,
  bizStatus: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  operator: undefined,
  operatorDepart: undefined,
  auditDocumentNo: undefined,
  reviewDocumentNo: undefined,
  publicStartTime: undefined,
  publicEndTime: undefined,
  rentType: undefined,
  publicDate: undefined,
  bearerObject: undefined,
  totalArea: undefined,
  referencePrice: undefined,
  limitPrice: undefined,
  price: undefined,
  rentMonths: undefined,
  priceIncrease: undefined,
  managerange: undefined,
  environmental: undefined,
  supporting: undefined,
  advantage: undefined,
  redecorateReq: undefined,
  otherReq: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  attachmentIds: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined
})

const defaultColumns = [
  { title: '招租方案名称', dataIndex: 'title', width: 200, fixed: 'left' },
  { title: '业务时间', dataIndex: 'bizDate', width: 120 },
  {
    title: '审批状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '操作人', dataIndex: 'operator', width: 100 },
  { title: '招租总面积', dataIndex: 'totalArea', width: 120, customRender: ({ text }) => `${text}㎡` },
  { title: '原租金', dataIndex: 'originalRent', width: 100 },
  { title: '单位租金', dataIndex: 'price', width: 120, customRender: ({ text }) => `${text}元/㎡` },
  { title: '经营范围', dataIndex: 'managerange', width: 140 },
  { title: '租赁期限', dataIndex: 'rentMonths', width: 100, customRender: ({ text }) => `${text}个月` },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_RentScheme_BizStatus', 'dot')
  },
  { title: '中标结果', dataIndex: 'wyBuildingCount', width: 100 },
  { title: '签约合同', dataIndex: 'wyBuildingCount', width: 100 },
  { title: '方案编码', dataIndex: 'wyBuildingCount', width: 100 },
  { title: '流标次数', dataIndex: 'wyBuildingCount', width: 100 },
  { title: '变更次数', dataIndex: 'wyBuildingCount', width: 100 },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.children = []
  })
  return list
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRentSchemeRef = ref()
const handleAdd = () => {
  editRentSchemeRef.value.open()
}
const handleEdit = (data) => {
  editRentSchemeRef.value.open(data.id)
}

const rentSchemeDetailRef = ref()
const handleView = (data) => {
  logList({ parent: data.id })
  rentSchemeDetailRef.value.open(data.id)
}

const handleViewCustomer = () => {}
const handleViewContract = () => {}

const handleAudit = async (record, result) => {
  result ? await audit({ id: record.id }) : await unAudit({ id: record.id })
  onTableChange(pagination.value)
}

const handleExpand = async (expand, record) => {
  const { result } = await logList({ parent: record.id })
  record.children = result.records.map((i) => ({
    name: i.name,
    bizDate: '',
    status: '',
    operator: i.operator,
    totalArea: record.totalArea,
    originalRent: record.originalRent,
    price: record.price,
    managerange: record.managerange,
    rentMonths: record.rentMonths,
    bizStatus: i.bizStatus
  }))
}

const setResultRef = ref()
const handleSetResult = (record) => {
  setResultRef.value.open(record.id)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除招租方案？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('招租方案数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
