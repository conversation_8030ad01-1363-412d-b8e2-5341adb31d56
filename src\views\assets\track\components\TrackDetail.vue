<template>
  <a-drawer v-model:open="visible" :mask-closable="false" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === 0 }"
            @click="handleSwitch(1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === list.length - 1 }"
            @click="handleSwitch()"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn">查看审核记录</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn">反审核</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="handleDel">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <!-- ：{{ detailData.name }} -->
        <h2 class="text-[18px] font-bold mr-[12px]">
          {{ renderDict(detailData.trackingType, 'CT_BASE_ENUM_TrackingType') }}资产跟踪
        </h2>
        <a-tag :type="getStatusColor(detailData.status)">
          {{ detailData.status_dictText }}
        </a-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>

      <div>
        <div class="text-[16px] font-bold mb-[12px] text-secondary">跟踪信息</div>
        <!-- 闲置 -->
        <div v-if="detailData.trackingType === 'Idle'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">资产：{{ detailData.houseOwner_dictText || '-' }}</span>
          <span class="w-[50%]">闲置面积：{{ detailData.unUsedArea || '-' }}</span>
          <span class="w-[50%]">闲置状态开始时间：{{ detailData.unUsedBeginDate || '-' }}</span>
          <span class="w-[50%]">闲置状态结束时间：{{ detailData.unUsedEndDate || '-' }}</span>
          <span class="w-[50%]">闲置原因：{{ detailData.unUsedReason || '-' }}</span>
          <span class="w-[50%]">闲置时间：{{ detailData.unUsedTime || '-' }}</span>
          <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
        <!-- 占用 -->
        <div v-if="detailData.trackingType === 'Occupy'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">资产：{{ detailData.houseOwner_dictText || '-' }}</span>
          <span class="w-[50%]">占用面积：{{ detailData.occupyArea || '-' }}</span>
          <span class="w-[50%]">占用人：{{ detailData.occupyPerson || '-' }}</span>
          <span class="w-[50%]">占用原因：{{ detailData.occupyReason || '-' }}</span>
          <span class="w-[50%]">占用状态开始时间：{{ detailData.occupyBeginDate || '-' }}</span>
          <span class="w-[50%]">占用状态结束时间：{{ detailData.occupyEndDate || '-' }}</span>
          <span class="w-[50%]">其他情况：{{ '-' }}</span>
          <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
        <!-- 借用 -->
        <div v-if="detailData.trackingType === 'Borrow'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">资产：{{ detailData.houseOwner_dictText || '-' }}</span>
          <span class="w-[50%]">借用面积：{{ '-' }}</span>
          <span class="w-[50%]">借用人：{{ detailData.landRent || '-' }}</span>
          <span class="w-[50%]">借用原因：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">借用状态开始时间：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">借用状态结束时间：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">其他情况：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
        <!-- 自用 -->
        <div v-if="detailData.trackingType === 'Self'" class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">资产：{{ detailData.houseOwner_dictText || '-' }}</span>
          <span class="w-[50%]">借用面积：{{ '-' }}</span>
          <span class="w-[50%]">借用人：{{ detailData.landRent || '-' }}</span>
          <span class="w-[50%]">借用原因：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">借用状态开始时间：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">借用状态结束时间：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">其他情况：{{ detailData.arrearsAmount || '-' }}</span>
          <span class="w-[50%]">备注：{{ detailData.remark || '-' }}</span>
        </div>
      </div>
      <!-- 闲置 占用 才有 -->
      <div v-if="['Idle', 'Occupy'].includes(detailData.trackingType)" class="mt-[40px]">
        <div class="flex justify-between items-center mb-[12px]">
          <div class="text-[16px] font-bold text-secondary">盘活记录</div>
          <a-button type="primary" ghost>
            <span class="a-icon-plus mr-[8px]"></span>
            添加记录
          </a-button>
        </div>
        <a-table :data-source="tableList" :columns="columns" :scroll="{ y: 300, x: 900 }" :pagination="false">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <span class="primary-btn" @click="rowEdit(record)">编辑</span>
              <span class="primary-btn" @click="rowDel(record)">删除</span>
            </template>
          </template>
        </a-table>
      </div>
    </a-spin>

    <template #footer>
      <a-button>结束</a-button>
    </template>
  </a-drawer>
  <!-- 编辑 -->
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
  <!-- 盘活记录编辑 -->
  <asset-revitalization-info-edit
    ref="assetRevitalizationInfoEditRef"
    @refresh="getRecords(detailData.id)"
  ></asset-revitalization-info-edit>
</template>
<script setup>
import {
  idleDelById,
  occupyDelById,
  borrowDelById,
  selfDelById,
  idleQueryById,
  occupyQueryById,
  borrowQueryById,
  selfQueryById,
  queryIdleAssetActivateByMainId,
  queryOccupyAssetActivateByMainId,
  queryBorrowAssetTrackingInfoByMainId,
  querySelfAssetTrackingInfoByMainId,
  assetRevInfoDel
} from '../apis'
import AssetRevitalizationInfoEdit from './AssetRevitalizationInfoEdit.vue'
import AddEdit from './AddEdit.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDict } from '@/utils/render'
const emits = defineEmits(['loadData'])
const { list } = defineProps({
  list: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  detailData.value.trackingType = data.trackingType
  visible.value = true
  if (data.id) {
    const activeIndex = list.findIndex((item) => item.id === data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    getDetailById(data.id)
  }
}
defineExpose({ open })
const tableList = ref([])
// 盘活记录列数组
const columns = computed(() => {
  let recordList = []
  switch (detailData.value.trackingType) {
    case 'Idle':
      recordList = [
        { title: '盘活措施填报日期', dataIndex: 'fillDate', width: 180, fixed: 'left' },
        { title: '已采取的盘活管理措施', dataIndex: 'ctrlMeasure' },
        { title: '下一步盘活建议', dataIndex: 'activateAdvise' },
        { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
      ]
      break
    case 'Occupy':
      recordList = [
        { title: '盘活措施填报日期', dataIndex: 'fillDate', width: 180, fixed: 'left' },
        { title: '已采取的盘活管理措施', dataIndex: 'ctrlMeasure' },
        { title: '下一步盘活建议', dataIndex: 'activateAdvise' },
        { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
      ]
      break
    case 'Borrow':
      recordList = [
        { title: '盘活措施填报日期', dataIndex: 'borrowBeginDate', width: 180, fixed: 'left' },
        { title: '已采取的盘活管理措施', dataIndex: 'manageCompany_dictText' },
        { title: '下一步盘活建议', dataIndex: 'status' },
        { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
      ]
      break
    case 'Self':
      recordList = [
        { title: '盘活措施填报日期', dataIndex: 'number', width: 180, fixed: 'left' },
        { title: '已采取的盘活管理措施', dataIndex: 'manageCompany_dictText' },
        { title: '下一步盘活建议', dataIndex: 'status' },
        { title: '操作', dataIndex: 'action', width: 200, fixed: 'right' }
      ]
      break
  }
  return recordList
})

const detailData = ref({})
const loading = ref(false)
// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    AUDITOK: 'success', // 审核通过
    AUDITING: 'processing', // 审核中
    TEMP: 'default', // 暂存
    AUDITNO: 'error', // 审核不通过
    BACK: 'warning', // 已撤回
    ENABLE: 'success', // 启用
    DISABLE: 'error', // 禁用
    CLOSED: 'default', // 关闭
    INTEND: 'blue' // 意向
  }
  return statusMap[status] || 'default'
}

const handleSwitch = (type) => {
  // 上一条
  if (type) {
    if (curIndex.value > 0) {
      curIndex.value--
      detailData.value.trackingType = list[curIndex.value].trackingType
      getDetailById(list[curIndex.value].id)
    }
    return
  }
  // 下一条
  if (curIndex.value < list.length - 1) {
    curIndex.value++
    detailData.value.trackingType = list[curIndex.value].trackingType
    getDetailById(list[curIndex.value].id)
  }
}

// 通过id获取详情
const requestFuncObj = computed(() => {
  const func = {
    delById: null,
    queryById: null,
    queryAssetActivateByMainId: null
  }
  switch (detailData.value.trackingType) {
    case 'Idle':
      func.queryById = idleQueryById
      func.delById = idleDelById
      func.queryAssetActivateByMainId = queryIdleAssetActivateByMainId
      break
    case 'Occupy':
      func.queryById = occupyQueryById
      func.delById = occupyDelById
      func.queryAssetActivateByMainId = queryOccupyAssetActivateByMainId
      break
    case 'Borrow':
      func.queryById = borrowQueryById
      func.delById = borrowDelById
      func.queryAssetActivateByMainId = queryBorrowAssetTrackingInfoByMainId
      break
    case 'Self':
      func.queryById = selfQueryById
      func.delById = selfDelById
      func.queryAssetActivateByMainId = querySelfAssetTrackingInfoByMainId
      break
  }
  return func
})
// 获取详情
const getDetailById = async (id) => {
  const { result } = await requestFuncObj.value.queryById(id)
  detailData.value = result
  getRecords(id)
}
// 获取盘活记录
const getRecords = async (id) => {
  const { result } = await requestFuncObj.value.queryAssetActivateByMainId(id)
  tableList.value = result
}

// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '提示',
    content: '确认删除当前资产？',
    centered: true,
    onOk: async () => {
      await requestFuncObj.value.delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      visible.value = false
    }
  })
}

// 盘活记录编辑和删除
const assetRevitalizationInfoEditRef = ref()
const rowEdit = (row) => {
  assetRevitalizationInfoEditRef?.value.open(row)
}
const rowDel = (row) => {
  Modal.confirm({
    title: '提示',
    content: '确认删除盘活记录？',
    centered: true,
    onOk: async () => {
      const data = await assetRevInfoDel(row.id)
      message.success(data.message)
      getRecords(detailData.value.id)
    }
  })
}
</script>
