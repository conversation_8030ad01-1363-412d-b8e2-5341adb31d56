<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex items-center">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新增
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="handleRemove(false)">删除</a-menu-item>
              <a-menu-item @click="handleFreeze(false, userStatus.FROZEN)">冻结</a-menu-item>
              <a-menu-item @click="handleFreeze(false, userStatus.NORMAL)">解冻</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-form layout="inline" ref="formRef" class="!ml-[40px]" autocomplete="off">
          <a-form-item label="账号">
            <s-input v-model="params.username" placeholder="请输入账号" allow-clear @input="handleInput"></s-input>
          </a-form-item>
          <a-form-item label="名字">
            <s-input v-model="params.realname" placeholder="请输入名字" allow-clear @input="handleInput"></s-input>
          </a-form-item>
        </a-form>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'avatar'">
          <a-avatar :src="getFileAccessHttpUrl(record.avatar)"></a-avatar>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleDetail(record)">详情</a-menu-item>
                <a-menu-item @click="handlePassword(record)">密码</a-menu-item>
                <a-menu-item @click="handleRemove(record)">删除</a-menu-item>
                <a-menu-item @click="handleFreeze(record)">
                  {{ record.status === userStatus.NORMAL ? '冻结' : '解冻' }}
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit-user ref="editUserRef" @refresh="refreshData"></edit-user>
    <modify-password ref="modifyPasswordRef"></modify-password>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { message, Modal } from 'ant-design-vue'
import { getUserList, deleteUser, frozenBatch } from './apis'
import EditUser from './components/EditUser.vue'
import { getFileAccessHttpUrl } from '@/apis/common'
import useTableSelection from '@/hooks/useTableSelection'
import { renderDict } from '@/utils/render'
import ModifyPassword from './components/ModifyPassword.vue'
import { useDictStore } from '@/store/modules/dict'

const route = useRoute()
const pageTitle = computed(() => route.meta.title)

const { userStatus } = useDictStore()

const params = reactive({
  column: 'createTime',
  order: 'desc',
  username: '',
  realname: '',
  sex: '',
  phone: '',
  status: ''
})

const defaultColumns = [
  { title: '用户账号', dataIndex: 'username', width: 120, fixed: 'left' },
  { title: '用户姓名', dataIndex: 'realname', width: 100 },
  { title: '头像', dataIndex: 'avatar', width: 120 },
  { title: '性别', dataIndex: 'sex', width: 80, customRender: ({ text }) => renderDict(text, 'sex') },
  { title: '生日', dataIndex: 'birthday', width: 100 },
  { title: '手机号', dataIndex: 'phone', width: 100 },
  { title: '部门', width: 150, dataIndex: 'orgCodeTxt' },
  { title: '负责部门', width: 150, dataIndex: 'departIds_dictText' },
  { title: '状态', dataIndex: 'status_dictText', width: 80 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getUserList)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const refreshData = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editUserRef = ref()
const handleAdd = () => {
  editUserRef.value.open()
}
const handleEdit = (row) => {
  editUserRef.value.open(row)
}

const handleDetail = (row) => {
  editUserRef.value.open(row, true)
}

const modifyPasswordRef = ref()
const handlePassword = (data) => {
  modifyPasswordRef.value.open(data)
}

/**
 * 冻结/解冻用户
 * @param {Boolean | Object} data 当data为false时，表示批量冻结/解冻
 * @param {Number} status 冻结/解冻状态，只有批量操作时，才有此参数
 */
const handleFreeze = (data, status) => {
  Modal.confirm({
    title: '系统提示',
    content: data
      ? `是否冻结用户“${data.realname}”`
      : `是否确认${status === userStatus.NORMAL ? '解冻' : '冻结'}选中用户？`,
    centered: true,
    onOk: async () => {
      await frozenBatch({
        ids: data ? data.id : selectedRowKeys.value.join(','),
        status: status || (data.status === userStatus.NORMAL ? userStatus.FROZEN : userStatus.NORMAL)
      })
      let msg = ''
      if (status) {
        msg = status === userStatus.NORMAL ? '已解冻' : '已冻结'
      } else {
        msg = data.status === userStatus.NORMAL ? '已冻结' : '已解冻'
      }
      message.success(msg)
      clearSelection()
      onTableChange(pagination.value)
    }
  })
}

/**
 * 删除用户
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '系统提示',
    content: data ? `是否确认删除用户“${data.realname}”？` : '是否确认删除选中用户？',
    centered: true,
    onOk: async () => {
      await deleteUser({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

onMounted(() => {
  onTableChange()
})
</script>
