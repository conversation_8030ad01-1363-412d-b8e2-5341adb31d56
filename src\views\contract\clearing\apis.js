import request from '@/apis/http'

export const page = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/clearing/list',
    params
  })
}

export const detail = (params) => {
  return request({
    method: 'get',
    url: '/biz/contractmanage/clearing/queryById',
    params
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/clearing/exportXls',
    params,
    responseType: 'blob',
    fileName,
    timeout: 60 * 5 * 1000
  })
}

export const importExcel = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/clearing/importExcel',
    data
  })
}

export const audit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/clearing/audit',
    data
  })
}

export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/clearing/unAudit',
    data
  })
}

export const save = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/clearing/add',
    data
  })
}

export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/clearing/submit',
    data
  })
}

export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/contractmanage/clearing/edit',
    data
  })
}

export const deleteBatch = (params) => {
  return request({
    method: 'delete',
    url: '/biz/contractmanage/clearing/deleteBatch',
    params
  })
}
