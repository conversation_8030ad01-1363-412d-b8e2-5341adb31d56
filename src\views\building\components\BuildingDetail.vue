<template>
  <a-drawer
    v-model:open="visible"
    class="building-detail-drawer common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitchBuilding(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchBuilding(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEditBuilding">编辑</span>
          <span class="primary-btn" @click="handleBuildingStatus">
            {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
          </span>
          <span class="primary-btn" @click="handleDeleteBuilding">删除</span>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">楼栋详情: {{ detail.name }}</h2>
        <span class="bg-[#edfbe2] rounded-[8px] text-success leading-[28px] px-[8px]" v-if="detail.status === 'ENABLE'">
          已启用
        </span>
        <span
          class="bg-[#e6e9f0] rounded-[8px] text-tertiary leading-[28px] px-[8px]"
          v-if="detail.status === 'DISABLE'"
        >
          已禁用
        </span>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.updateBy_dictText }} 提交于{{ detail.updateTime }}</span>
      </div>
      <div>
        <h2 class="text-[16px] font-bold mb-[12px]">楼栋基础信息</h2>
        <a-row :gutter="20" class="text-secondary">
          <a-col :span="12">楼栋名称: {{ detail.name }}</a-col>
          <a-col :span="12">所属项目: {{ detail.wyProject_dictText }}</a-col>
        </a-row>
        <a-row :gutter="20" class="text-secondary my-[12px]">
          <a-col :span="12">项目所属公司: {{ detail.company_dictText }}</a-col>
          <a-col :span="12">状态: {{ detail.status === 'ENABLE' ? '已启用' : '已禁用' }}</a-col>
        </a-row>
        <div class="text-secondary">备注: {{ detail.remark }}</div>
      </div>
      <h2 class="text-[16px] font-bold mt-[40px] mb-[12px]">楼层信息</h2>
      <a-table :data-source="floorList" :columns="columns" :pagination="false">
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-if="column.dataIndex === 'leaseUnitCount'">
            <span v-if="record.leaseUnitCount === 0">0</span>
            <span class="primary-btn" v-else>查看({{ record.leaseUnitCount }})</span>
          </template>
        </template>
      </a-table>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { buildingDetail, queryFloor, deleteBuilding, updateStatus } from '../apis/building.js'
import { message, Modal } from 'ant-design-vue'

// dataList: 外面表格数据，用于上一条/下一条
const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const currentIndex = ref(0)
const handleSwitchBuilding = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  loadDetail(dataList[index].id)
}

const emit = defineEmits(['editBuilding', 'refresh'])

const visible = ref(false)
const open = (id) => {
  visible.value = true
  loadDetail(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const loading = ref(false)
const detail = reactive({
  id: '',
  name: '',
  company_dictText: '',
  number: '',
  remark: '',
  updateTime: '',
  updateBy_dictText: '',
  status: ''
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await buildingDetail({ id })
  Object.assign(detail, result)
  await loadFloorList()
  loading.value = false
}

const handleEditBuilding = () => {
  visible.value = false
  emit('editBuilding', detail)
}

const handleBuildingStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该楼栋？`,
    content: detail.status === 'ENABLE' ? '楼栋禁用后将无法再被使用，但不影响已创建的数据。' : '',
    centered: true,
    onOk: async () => {
      await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(detail.status === 'ENABLE' ? '已禁用' : '启用成功')
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDeleteBuilding = () => {
  Modal.confirm({
    title: '确认删除该楼栋？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBuilding({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const columns = [
  { title: '#', dataIndex: 'index', width: 60 },
  { title: '楼层名称', dataIndex: 'name' },
  { title: '租赁单元', dataIndex: 'leaseUnitCount' }
]
const floorList = ref([])
const loadFloorList = async () => {
  const { result } = await queryFloor({ id: detail.id })
  floorList.value = result
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.building-detail-drawer {
  .building-item {
    border-radius: 8px;
    border: 1px solid #e6e9f0;
    background-color: #f7f8fa;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    transition:
      border-color 0.2s,
      background-color 0.2s;
    cursor: pointer;
    &:hover {
      border-color: var(--color-primary);
    }
    &.active {
      border-color: var(--color-primary);
      background-color: #eaf0fe;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .floor-section {
    flex: 1;
    border: 1px solid #e6e9f0;
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
