<template>
  <a-modal
    v-model:open="visible"
    class="common-modal"
    width="60%"
    title="租赁单元选择"
    :mask-closable="false"
    @ok="handleConfirm"
  >
    <div class="flex mb-[24px]">
      <lease-unit-tree-filter class="w-[280px]" @treeNodeChange="handleTreeNodeChange" />
      <s-input
        class="!w-[280px] ml-[16px]"
        v-model="search.number"
        placeholder="搜索编号"
        @input="handleSearch"
      ></s-input>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>
<script setup>
import LeaseUnitTreeFilter from '@/views/leaseUnit/manage/components/LeaseUnitTreeFilter.vue'
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getF7List } from '@/views/leaseUnit/manage/apis/leaseUnit.js'
const { multiple } = defineProps({
  // 默认单选
  multiple: { default: false, type: Boolean }
})
const emits = defineEmits(['selectChange'])
// 弹窗可见性
const visible = ref(false)
/**
 * 打开弹窗
 */
const open = () => {
  visible.value = true
  onTableChange()
}
defineExpose({ open })
const search = ref({})
const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 200, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 120 },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', width: 120 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 120, ellipsis: true },
  { title: '数据状态', dataIndex: 'status', width: 120 },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 150, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期时间', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权', dataIndex: 'property', width: 120 },
  { title: '项目', dataIndex: 'wyProject_dictText', width: 180, ellipsis: true },
  { title: '租赁单元编号', dataIndex: 'number', width: 200 }
]
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getF7List)
const { selectedRowKeys, selectedRows, onSelectChange } = useTableSelection(list, 'id', !multiple)

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}

/**
 * 搜索输入防抖处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      current: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}
/**
 * 处理树节点变更
 */
const handleTreeNodeChange = (nodeId) => {
  search.value.treeId = nodeId
  onTableChange({ current: 1 })
}

// 选择确定
const handleConfirm = () => {
  if (!selectedRows.value.length) {
    return message.warning('至少选择一条数据！')
  }
  emits('selectChange', selectedRows.value)
  visible.value = false
}
</script>
