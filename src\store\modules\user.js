import { login, queryPermissions, logout } from '@/apis/user'
import router, { createRouters } from '@/router'

export const useUserStore = defineStore('user', {
  persist: {
    key: `${import.meta.env.VITE_STORE_KEY}:user`
  },
  state: () => ({
    departs: [],
    token: '',
    userInfo: {},
    permission: {
      menu: []
    }
  }),
  actions: {
    async loginByUsername(params) {
      const { result } = await login(params)
      this.$patch((state) => {
        state.departs = result.departs || []
        state.token = result.token
        state.userInfo = result.userInfo
      })
      await this.getPermissions()
    },
    async getPermissions() {
      const { result } = await queryPermissions()
      this.permission = result
      createRouters(result.menu)
    },
    async logout() {
      await logout()
      this.$patch((state) => {
        state.departs = []
        state.token = ''
        state.userInfo = {}
        state.permission = { menu: [] }
      })
      router.replace('/login')
    }
  }
})

export default function userStore() {
  const store = useUserStore()
  return storeToRefs(store)
}
