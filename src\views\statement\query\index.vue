<template>
  <div>
    <div class="flex justify-between">
      <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
      <a-button type="primary" ghost>
        <span class="a-icon-statistics mr-[8px]"></span>
        统计分析
      </a-button>
    </div>

    <div class="!my-[24px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item label="公司">
          <dept-tree-select v-model="search.company" placeholder="请选择公司" class="!w-[280px]"></dept-tree-select>
        </a-form-item>
        <a-form-item label="月份">
          <a-date-picker
            class="w-[280px]"
            v-model:value="search.month"
            picker="month"
            value-format="YYYY-MM"
            format="YYYY-MM"
            placeholder="请选择月份"
          />
        </a-form-item>
        <a-form-item label="客户">
          <user-select
            class="!w-[280px]"
            v-model="search.followPerson"
            v-model:display-value="search.followPerson_dictText"
            placeholder="请选择客户"
            title="请选择客户"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="onTableChange(1)">查询</a-button>
          <a-button @click="searchReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <div class="flex justify-center relative pb-[24px]">
      <div class="h-[32px] text-[16px] font-bold flex items-center">ABC公司-2025年1月账单</div>
      <div class="absolute top-0 right-0">
        <a-button type="primary" ghost>导出账单</a-button>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="defaultColumns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="false"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
    ></a-table>
    <div class="flex flex-col items-end mt-[20px] p-[20px] bg-[#F5F5F5] font-bold text-[14px] rounded-[8px]">
      <div>
        <span class="w-[120px] inline-block">含税金额合计：</span>
        <span class="w-[120px] text-end inline-block">50000.00</span>
      </div>
      <div class="mt-[10px] mb-[10px]">
        <span class="w-[120px] inline-block">已核销金额合计：</span>
        <span class="w-[120px] text-end inline-block">50000.00</span>
      </div>
      <div>
        <span class="w-[120px] inline-block">未核销金额合计：</span>
        <span class="w-[120px] text-end inline-block">0.00</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { renderDict } from '@/utils/render'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage } from './apis'
onMounted(() => {
  onTableChange()
})
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const search = ref({
  company: '',
  month: '',
  followPerson: ''
})
const defaultColumns = [
  { title: '客户', dataIndex: 'name', width: 150, fixed: true },
  { title: '款项类型', dataIndex: 'ownerNumber', width: 150 },
  { title: '应收日期', dataIndex: 'propertyUse', width: 200 },
  {
    title: '开始日期',
    dataIndex: 'landNature',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  },
  { title: '结束日期', dataIndex: 'detailAddress', minWidth: 80 },
  { title: '收入归属年月', dataIndex: '', minWidth: 80 },
  { title: '合同号', dataIndex: '', minWidth: 80 },

  { title: '含税金额', dataIndex: 'structureArea', minWidth: 80 },
  { title: '已核销金额', dataIndex: 'floorArea', minWidth: 80 },
  {
    title: '未核销金额',
    dataIndex: 'houseType',
    minWidth: 80,
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_HouseType')
  },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
// pagination
const { list, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id', true)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, houseDealBill: JSON.stringify(search.value) })
}
// 重置
const searchReset = () => {
  Object.assign(search.value, {})
  onTableChange(1)
}
</script>
