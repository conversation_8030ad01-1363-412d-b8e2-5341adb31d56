import request from '@/apis/http'

// 懒加载获取部门树
export const getDepartTreeLazy = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/queryDepartTreeSync',
    params
  })
}

export const getDepartTree = () => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/queryTreeList'
  })
}

export const searchDepart = (params) => {
  return request({
    method: 'get',
    url: '/sys/sysDepart/searchBy',
    params
  })
}

export const addDept = (data) => {
  return request({
    method: 'post',
    url: '/sys/sysDepart/add',
    data
  })
}

export const editDept = (data) => {
  return request({
    method: 'post',
    url: '/sys/sysDepart/edit',
    data
  })
}

export const deleteDept = (params) => {
  return request({
    method: 'delete',
    url: '/sys/sysDepart/deleteBatch',
    params
  })
}

export const queryDepartPermission = (params) => {
  return request({
    method: 'get',
    url: '/sys/permission/queryDepartPermission',
    params
  })
}

export const saveDepartPermission = (data) => {
  return request({
    method: 'post',
    url: '/sys/permission/saveDepartPermission',
    data
  })
}
