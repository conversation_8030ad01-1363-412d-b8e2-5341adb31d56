<template>
  <div class="user-select-input" @click="openModal" @mouseenter="onmouseenter" @mouseleave="onmouseleave">
    <span :placeholder="placeholder">{{ displayValue }}</span>
    <i class="a-icon-close-solid text-[rgba(23,43,82,0.25)]" @click.stop="handleClear" v-if="showClearBtn"></i>
    <i class="a-icon-arrow-down text-[rgba(23,43,82,0.25)]" v-else></i>
  </div>

  <a-modal
    v-model:open="visible"
    :title="title"
    width="800px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <a-form autocomplete="off" layout="inline" class="!mb-[16px]">
      <a-form-item label="编码(表号)">
        <a-input
          v-model:value="params.number"
          placeholder="搜索编码(表号)"
          @input="handleInput"
          style="width: 220px"
          allow-clear
        >
          <template #prefix>
            <i class="a-icon-search text-primary"></i>
          </template>
        </a-input>
      </a-form-item>
      <a-form-item label="水电表名称">
        <a-input
          v-model:value="params.name"
          placeholder="搜索水电表名称"
          @input="handleInput"
          style="width: 220px"
          allow-clear
        >
          <template #prefix>
            <i class="a-icon-search text-primary"></i>
          </template>
        </a-input>
      </a-form-item>
    </a-form>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: 'radio'
      }"
      :scroll="{ y: '50vh' }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import {
  getWaterElectricityList,
  queryWaterElectricityById
} from '@/views/waterElectricity/manage/apis/waterElectricity'

const { modelValue } = defineProps({
  modelValue: { type: [String, Number], default: null },
  placeholder: { type: String, default: '请选择水电表' },
  title: { type: String, default: '选择水电表' }
})

const emits = defineEmits(['update:modelValue', 'change'])

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getWaterElectricityList)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id', true)

const visible = ref(false)
const showClear = ref(false)
let timer

const displayValue = ref('')
const params = reactive({
  number: undefined,
  name: undefined
})

const showClearBtn = computed(() => modelValue && showClear.value)

const columns = [
  { title: '编码(表号)', dataIndex: 'number', width: 200 },
  { title: '名称', dataIndex: 'name', width: 160, ellipsis: true },
  { title: '类型', dataIndex: 'type_dictText', width: 120 },
  { title: '属性', dataIndex: 'property_dictText', width: 120 },
  { title: '地址', dataIndex: 'address', width: 160, ellipsis: true },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160, ellipsis: true }
]

/**
 * 打开选择模态框
 */
const openModal = () => {
  visible.value = true
  onTableChange()
}

/**
 * 取消选择模态框
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 清空选择
 */
const handleClear = () => {
  emits('update:modelValue', null)
  emits('change', null)
  displayValue.value = ''
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length > 0) {
    const selectedItem = selectedRows.value[0]
    const value = selectedRowKeys.value[0]
    displayValue.value = selectedItem.name || selectedItem.number
    emits('update:modelValue', value)
    emits('change', selectedItem)
    handleCancel()
  }
}

/**
 * 搜索输入处理
 */
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 表格分页变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

/**
 * 鼠标进入事件
 */
const onmouseenter = () => {
  if (modelValue) {
    showClear.value = true
  }
}

/**
 * 鼠标离开事件
 */
const onmouseleave = () => {
  showClear.value = false
}

/**
 * 监听 modelValue 变化，更新显示值
 */
watch(
  () => modelValue,
  async (val) => {
    clearSelection()
    displayValue.value = ''

    if (!val || val === null || val === undefined || val === '') {
      return
    }

    const { result } = await queryWaterElectricityById({ id: val })

    selectedRowKeys.value = [val]
    selectedRows.value = [result]
    if (selectedRows.value.length > 0) {
      const selectedItem = selectedRows.value[0]
      displayValue.value = selectedItem.name || selectedItem.number
    }
  },
  { immediate: true }
)

/**
 * 暴露给父组件的方法
 */
defineExpose({
  open: openModal
})
</script>

<style lang="less" scoped>
.user-select-input {
  width: 100%;
  cursor: pointer;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
  &:hover {
    border-color: var(--color-primary);
  }
  & > span:empty {
    &::after {
      content: attr(placeholder);
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
</style>
