<template>
  <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '140px' } }" autocomplete="off">
    <a-form-item label="公告标题" name="title">
      <a-input v-model:value="form.title" :maxlength="50" show-count></a-input>
    </a-form-item>
    <a-form-item label="物业管理公司" name="manageCompany">
      <dept-tree-select v-model="form.manageCompany" :allow-clear="false"></dept-tree-select>
    </a-form-item>
    <a-form-item label="业务日期" name="bizDate">
      <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD"></a-date-picker>
    </a-form-item>
    <a-form-item label="经办人" name="operator">
      <a-input v-model:value="form.operator" :maxlength="10"></a-input>
    </a-form-item>
    <a-form-item label="批复文号" name="auditDocumentNo">
      <a-input v-model:value="form.auditDocumentNo" :maxlength="50"></a-input>
    </a-form-item>
    <a-form-item label="过会文号" name="reviewDocumentNo">
      <a-input v-model:value="form.reviewDocumentNo" :maxlength="50"></a-input>
    </a-form-item>
    <a-form-item label="公示开始日期" name="publicStartTime">
      <a-date-picker v-model:value="form.publicStartTime" value-format="YYYY-MM-DD"></a-date-picker>
    </a-form-item>
    <a-form-item label="公示结束日期" name="publicEndTime">
      <a-date-picker v-model:value="form.publicEndTime" value-format="YYYY-MM-DD"></a-date-picker>
    </a-form-item>
    <a-form-item label="招租方式" name="rentType">
      <dict-select v-model="form.rentType" code="CT_BAS_RentalMothed"></dict-select>
    </a-form-item>
    <a-form-item label="招标信息发布日期" name="publicDate">
      <a-date-picker v-model:value="form.publicDate" value-format="YYYY-MM-DD"></a-date-picker>
    </a-form-item>
  </a-form>
</template>

<script setup>
const { form } = defineProps({
  form: { type: Object, required: true }
})

const validateEndTime = (_, value) => {
  if (!value) return Promise.resolve()
  if (!form.publicStartTime) return Promise.resolve()
  if (new Date(value) < new Date(form.publicStartTime)) {
    return Promise.reject('公示结束日期不得早于公示开始日期')
  }
  return Promise.resolve()
}
const rules = {
  title: [{ required: true, message: '请填写公告标题', trigger: 'blur' }],
  operator: [{ required: true, message: '请填写经办人', trigger: 'blur' }],
  rentType: [{ required: true, message: '请选择招租方式', trigger: 'change' }],
  publicDate: [{ required: true, message: '请选择招标信息发布日期', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  publicEndTime: [{ required: false, validator: validateEndTime, trigger: 'change' }]
}

const formRef = ref()

const validate = async () => {
  await formRef.value.validate()
}

const clearValidate = () => {
  formRef.value.clearValidate()
}

defineExpose({ validate, clearValidate })
</script>
