<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.name"
          placeholder="搜索名称"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleEdit(record)">编辑</a-menu-item>
                <a-menu-item @click="handleRemove(record)">删除</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit-payment-type ref="editPaymentTypeRef" @refresh="refresh"></edit-payment-type>
    <payment-type-detail
      ref="paymentTypeDetailRef"
      :data-list="list"
      @edit="handleEdit"
      @refresh="refreshFromDetail"
    ></payment-type-detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('款项类型导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, updateStatus } from './apis.js'
import EditPaymentType from './components/EditPaymentType.vue'
import PaymentTypeDetail from './components/PaymentTypeDetail.vue'
import { Modal, message } from 'ant-design-vue'

const route = useRoute()

const pageTitle = computed(() => route.meta.title)

const params = reactive({
  id: undefined,
  status: undefined,
  number: undefined,
  name: undefined,
  paymentProperties: undefined,
  manageCompany: undefined,
  contractType: undefined,
  periodPayContract: undefined,
  noInvoice: undefined,
  includeAccrualStamp: undefined,
  isDeposit: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  attachmentIds: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined
})

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '名称', dataIndex: 'name', width: 120 },
  { title: '款项类型', dataIndex: 'paymentProperties_dictText', width: 120 },
  { title: '单据状态', dataIndex: 'status', width: 120 },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 120 },
  { title: '不开票', dataIndex: 'noInvoice', width: 120, customRender: ({ text }) => (text ? '是' : '否') },
  { title: '周期性缴交', dataIndex: 'periodPayContract', width: 120, customRender: ({ text }) => (text ? '是' : '否') },
  { title: '押金', dataIndex: 'isDeposit', width: 120, customRender: ({ text }) => (text ? '是' : '否') },
  {
    title: '纳入计提印花税',
    dataIndex: 'includeAccrualStamp',
    width: 140,
    customRender: ({ text }) => (text ? '是' : '否')
  },
  { title: '合同协议类型', dataIndex: 'contractType_dictText', width: 120 },
  { title: '备注', dataIndex: 'remark', width: 160, ellipsis: true },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editPaymentTypeRef = ref()
const handleAdd = () => {
  editPaymentTypeRef.value.open()
}
const handleEdit = (data) => {
  editPaymentTypeRef.value.open(data.id)
}

const paymentTypeDetailRef = ref()
const handleView = (data) => {
  paymentTypeDetailRef.value.open(data.id)
}

const handleStatusChange = async (data, val) => {
  if (data.loading) return
  try {
    data.loading = true
    await updateStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
    data.loading = false
    message.success('保存成功')
    data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    data.loading = false
    data.checked = !val
  }
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除款项类型？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('款项类型数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>
