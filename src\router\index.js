import { createRouter, createWebHistory } from 'vue-router'
import BasicLayout from '@/layouts/BasicLayout.vue'
import ContentLayout from '@/layouts/ContentLayout.vue'
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: ContentLayout,
      redirect: '/home',
      children: [
        { path: 'home', component: () => import('@/views/home/<USER>'), meta: { title: '工作台' } },
        {
          path: 'personalCenter',
          component: () => import('@/views/personalCenter/index.vue'),
          meta: { title: '个人中心' }
        }
      ]
    },
    {
      path: '/login',
      meta: { title: '登录' },
      component: () => import('@/views/login/index.vue')
    }
  ]
})

export default router

const routeList = []
const setRouteList = (list) => {
  list.forEach((item) => {
    if (item.children && item.children.length) {
      setRouteList(item.children)
    } else {
      const component = getComponent(item)
      if (component) {
        routeList.push({
          path: item.path.replace('/', ''), // 需要把/system/user的第一个/去掉，因为要作为子路由添加到children中
          component,
          meta: item.meta
        })
      }
    }
  })
}

const modules = import.meta.glob('@/views/**/*.vue')
const modulePaths = Object.keys(modules)

/**
 * 根据菜单的path获取对应的vue组件
 * @param {Object} menu 菜单
 * @returns {Function | null} 对应组件()=>import(xxx)或者null
 */
const getComponent = (menu) => {
  const path = modulePaths.find((i) => i.endsWith(`${menu.path}.vue`) || i.endsWith(`${menu.path}/index.vue`))
  return path ? modules[path] : null
}

export const createRouters = (list) => {
  setRouteList(list)
  router.addRoute({
    path: '/',
    component: BasicLayout,
    children: routeList
  })
  // 注意，动态添加完路由以后，必须执行触发导航的方法，即push或者replace，根据官方文档说明：
  // 添加路由并不会触发新的导航。也就是说，除非触发新的导航，否则不会显示所添加的路由
  router.replace(decodeURIComponent(location.href).replace(location.origin + import.meta.env.BASE_URL, '/'))
}
