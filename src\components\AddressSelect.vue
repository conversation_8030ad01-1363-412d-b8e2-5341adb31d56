<!-- 地址选择弹窗 -->
<template>
  <div class="flex">
    <a-cascader
      class="!mr-[4px]"
      ref="cascaderRef"
      v-model:value="ruleForm.localPcaCode"
      :options="areaList"
      placeholder="请选择区域"
      @change="cascaderChange"
    />
    <a-input
      v-model:value="ruleForm.localAddress"
      placeholder="请输入详细地址"
      allow-clear
      :maxlength="64"
      @change="inputChange"
    ></a-input>
  </div>
</template>
<script setup>
import areaList from '@/json/region.json'
const emits = defineEmits(['setAddress', 'update:address', 'update:modelValue'])
const { modelValue, address } = defineProps({
  modelValue: { required: true, type: Array },
  address: { required: true, type: String }
})

const ruleForm = ref({
  localPcaCode: [],
  localAddress: ''
})

const cascaderChange = () => {
  emits('update:modelValue', ruleForm.value.localPcaCode)
}
const inputChange = () => {
  emits('update:address', ruleForm.value.localAddress)
}
onMounted(() => {
  ruleForm.value.localPcaCode = modelValue
  ruleForm.value.localAddress = address
})
</script>
