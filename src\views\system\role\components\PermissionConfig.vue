<template>
  <a-drawer
    v-model:open="visible"
    class="edit-menu-drawer"
    :title="`角色权限配置[${roleInfo.roleName}]`"
    placement="right"
    width="680px"
  >
    <div class="flex items-center mb-[20px]">
      <a-button type="primary" @click="handleCheckedAll(true)">选择全部</a-button>
      <a-button @click="handleCheckedAll(false)">取消全选</a-button>
      <span class="text-[18px] mx-[10px]">/</span>
      <a-button type="primary" @click="handleExpandAll(true)">展开全部</a-button>
      <a-button @click="handleExpandAll(false)">折叠全部</a-button>
      <span class="text-[18px] mx-[10px]">/</span>
      <a-button type="primary" @click="setCheckStrictly(false)">层级关联</a-button>
      <a-button @click="setCheckStrictly(true)">层级独立</a-button>
    </div>
    <a-tree
      v-model:expanded-keys="expandedKeys"
      v-model:checked-keys="checkedKeys"
      checkable
      :check-strictly="checkStrictly"
      :tree-data="treeData"
      :field-names="{ title: 'slotTitle' }"
    >
      <template #title="{ slotTitle }">
        <span>{{ slotTitle }}</span>
      </template>
    </a-tree>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" :loading="loading" @click="handleSave(false)">仅保存</a-button>
      <a-button type="primary" :loading="loading" @click="handleSave(true)">保存并关闭</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { queryTreeList, queryRolePermission, saveRolePermission } from '../apis'

const visible = ref(false)

const roleInfo = reactive({})

const open = (data) => {
  Object.assign(roleInfo, data)
  visible.value = true
  loadTreeData()
}

const treeData = ref([])
const allIds = ref([])
const loadTreeData = async () => {
  const { result } = await queryTreeList()
  treeData.value = result.treeList
  allIds.value = result.ids
  loadPermissionByRole()
}

const lastPermissionIds = ref([])
const loadPermissionByRole = async () => {
  const { result } = await queryRolePermission({ roleId: roleInfo.id })
  checkedKeys.value = result
  lastPermissionIds.value = [...result]
}

const expandedKeys = ref([])
const checkedKeys = ref([])

const handleCheckedAll = (checked) => {
  checkedKeys.value = checked ? allIds.value : []
}

const handleExpandAll = (expand) => {
  expandedKeys.value = expand ? allIds.value : []
}

const checkStrictly = ref(false)
const setCheckStrictly = (value) => {
  checkStrictly.value = value
}

const loading = ref(false)
const handleSave = async (needClose) => {
  if (loading.value) return
  try {
    loading.value = true
    await saveRolePermission({
      lastpermissionIds: lastPermissionIds.value.join(','),
      permissionIds: checkedKeys.value.join(','),
      roleId: roleInfo.id
    })
    loading.value = false
    message.success('保存成功')
    if (needClose) {
      handleCancel()
    }
  } catch {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>
