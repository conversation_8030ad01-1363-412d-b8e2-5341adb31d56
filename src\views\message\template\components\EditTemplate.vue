<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}消息模板`"
    class="common-drawer"
    placement="right"
    width="1072px"
  >
    <a-spin :spinning="confirmLoading">
      <a-form ref="formRef" :model="formData" :rules="rules">
        <h2 class="text-[16px] font-bold my-[24px]">基础信息</h2>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="模版标题" name="templateTitle">
              <a-input
                v-model:value="formData.templateTitle"
                placeholder="请输入模板标题"
                show-count
                :maxlength="100"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="模板类型" name="templateType">
              <dict-select v-model="formData.templateType" placeholder="模板类型" code="msgType"></dict-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="模板内容" name="templateContent">
          <a-textarea
            v-model:value="formData.templateContent"
            placeholder="请输入模板内容，可使用{变量名}作为占位符"
            :rows="10"
            show-count
            :maxlength="2000"
          />
        </a-form-item>
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">保存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { getTemplateById, addTemplate, updateTemplate } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

// 表单默认值
const formDataDefault = {
  id: '',
  templateCode: '',
  templateTitle: '',
  templateContent: '',
  templateType: 'plainText',
  isEnabled: true
}

// 表单数据
const formData = reactive({ ...formDataDefault })

// 表单验证规则
const rules = {
  templateCode: [
    { required: true, message: '请输入模板编码', trigger: 'blur' },
    { min: 2, max: 50, message: '模板编码长度应为2-50个字符', trigger: 'blur' }
  ],
  templateTitle: [
    { required: true, message: '请输入模板标题', trigger: 'blur' },
    { min: 2, max: 100, message: '模板标题长度应为2-100个字符', trigger: 'blur' }
  ],
  templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  templateContent: [
    { required: true, message: '请输入模板内容', trigger: 'blur' },
    { min: 2, max: 2000, message: '模板内容长度应为2-2000个字符', trigger: 'blur' }
  ]
}

/**
 * 打开抽屉
 * @param {String} id 模板ID，如果有则是编辑，否则是新增
 */
const open = async (id) => {
  resetForm()

  if (id) {
    confirmLoading.value = true
    try {
      const { data } = await getTemplateById({ id })
      Object.assign(formData, data)
    } finally {
      confirmLoading.value = false
    }
  }

  visible.value = true
}

/**
 * 重置表单
 */
const resetForm = () => {
  Object.assign(formData, formDataDefault)
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

/**
 * 保存表单
 */
const handleSave = async () => {
  try {
    await formRef.value.validate()

    confirmLoading.value = true

    if (formData.id) {
      await updateTemplate(formData)
      message.success('更新成功')
    } else {
      await addTemplate(formData)
      message.success('创建成功')
    }

    emits('refresh')
    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 取消编辑
 */
const handleCancel = () => {
  visible.value = false
  resetForm()
}

defineExpose({ open })
</script>
